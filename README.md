# auth-mservice project

This project uses Quarkus, the Supersonic Subatomic Java Framework.

If you want to learn more about Quarkus, please visit its website: https://quarkus.io/ .

## Running the application in dev mode

Before running the auth-mservice on your local, run the command below to add a DNS into your hosts file
```bash
sudo su && echo "127.0.0.1 auth.glideyoke.com" >> /etc/hosts
sudo su && echo "************ identity.glideyoke.com" >> /etc/hosts
```
Please remember to `replace ************` with your `keycloak local ip`
and ensure your host file has 2 new lines:
```text
************ identity.glideyoke.com
127.0.0.1 auth.glideyoke.com
```

You can run your application in dev mode that enables live coding using:
```shell script
./mvnw compile quarkus:dev
```
or 

`mvn clean compile -U -DskipTests quarkus:dev -Ddebug=5005`

Then, try to access the login API via browser `http://auth.glideyoke.com:8080`

> **_NOTE:_**  Quarkus now ships with a Dev UI, which is available in dev mode only at http://localhost:8080/q/dev/.

## Running the application as native mode
Run custom profile
`./mvnw package -Dquarkus.profile=stage`

Runable jar
`java -jar target/quarkus-app/quarkus-run.jar`

## Packaging and running the application

The application can be packaged using:
```shell script
./mvnw package
```
It produces the `quarkus-run.jar` file in the `target/quarkus-app/` directory.
Be aware that it’s not an _über-jar_ as the dependencies are copied into the `target/quarkus-app/lib/` directory.

If you want to build an _über-jar_, execute the following command:
```shell script
./mvnw package -Dquarkus.package.type=uber-jar
```

The application is now runnable using `java -jar target/quarkus-app/quarkus-run.jar`.

## Creating a native executable

You can create a native executable using: 
```shell script
./mvnw package -Pnative
```

Or, if you don't have GraalVM installed, you can run the native executable build in a container using: 
```shell script
./mvnw package -Pnative -Dquarkus.native.container-build=true
```

You can then execute your native executable with: `./target/auth-mservice-1.0.0-SNAPSHOT-runner`

If you want to learn more about building native executables, please consult https://quarkus.io/guides/maven-tooling.html.

### Exporting KeyCloak Setting
The steps below are used to export REALM

In my macbook pod name is: keycloak-6d6876b8d7-b4xjp , you can check your KEYCLOAK pod name by running
```shell script
kubectl get all
```

- Access to Running Pod (please change the pod name on your local machine)
```shell script
kubectl exec -it pod/keycloak-6d6876b8d7-b4xjp bash
```

- Run this script
```shell script
/opt/jboss/keycloak/bin/standalone.sh -Dkeycloak.migration.action=export -Dkeycloak.migration.provider=singleFile -Dkeycloak.migration.file=/opt/jboss/exported-keycloak-settings.json -Djboss.http.port=8888 -Djboss.https.port=9999 -Djboss.management.http.port=7777 -Djboss.management.https.port=7776
```

- After run this script above ( no more logs print out ), press Control + C to stop the running script, and now the realm is already located at : /opt/jboss/keycloak/keycloak-export.json ( inside kubenete pod)

- Exit the running pod by typing:
```shell script
exit
```

- Copy that file from kubernetes pod into our local machine, by running this:
```shell script
kubectl cp keycloak-6d6876b8d7-b4xjp:opt/jboss/exported-keycloak-settings.json ./doc/keycloak_configs/exported-keycloak-settings.json
```

### Importing the configuration into KeyCloak
- Copy that file from kubernetes pod into our local machine, by running this:
```shell script
kubectl cp ./doc/keycloak_configs/exported-keycloak-settings.json keycloak-6d6876b8d7-b4xjp:opt/jboss/exported-keycloak-settings.json
```
- Access to Running Pod (please change the pod name on your local machine)
```shell script
kubectl exec -it pod/keycloak-6d6876b8d7-b4xjp bash
```

- Run this script for Importing the configuration
```shell script
/opt/jboss/keycloak/bin/standalone.sh -Dkeycloak.profile.feature.upload_scripts=enabled -Dkeycloak.migration.action=import -Dkeycloak.migration.provider=singleFile -Dkeycloak.migration.file=/opt/jboss/exported-keycloak-settings.json -Djboss.http.port=8888 -Djboss.https.port=9999 -Djboss.management.http.port=7777 -Djboss.management.https.port=7776
```

### Testing Users:

#### Super Users:
- Master realm: superadmin/superadmin

#### Admin Users:
- Master realm: admin/admin

#### Normal Users:
- Master realm: tripudiotech/master

#### Accounts info:

```json
[
    {
        "username": "admin",
        "email": "<EMAIL>",
        "firstName": "Admin First Name",
        "lastName": "Admin Last Name",
        "enabled": true,
        "emailVerified": true,
        "createdTimestamp": *************,
        "id": "30f19308-0c12-46fe-8d05-97c41d117394"
    },
    {
        "username": "superadmin",
        "email": "<EMAIL>",
        "firstName": "Super First Name",
        "lastName": "Super Admin Last Name",
        "enabled": true,
        "emailVerified": true,
        "createdTimestamp": *************,
        "id": "bb7552a0-c949-400c-a074-fe00250fb3af"
    }
]
```

# REST API
[Swagger UI](http://localhost:8080/api)

## Login
### Requirements
- By default admin & superadmin are created automatically, you can login to them
- If you want to create your own account, please see the below section `Create New User`
### Request URL
`http://localhost:8080/authenticate`
### Request Body
- Authenticate using `Basic Auth` , enter `username / password` ( recommend to use postman)
### Request Header
- `X-Tenant-Id`: ${tenant} (eg: `master`)
### Response body 
```json
{
    "scope": "email profile",
    "access_token": "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJhVm5VQ0Z0MmZIYkdIaDhEUDE3YTFwcmVMRm5Sb0FqR1ZMQ1ZydFo3QkJNIn0.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.QrRX8axePX_6FtXzZcZewIywkRTKK7lYu3f_7pSXVv2wemEU8rIVvJBijAK1icJgP6h7R7eZ4ir1pSWa297temUzh4QcbjAwSGXUr48HdLhRqHgRdjkbGRTI3nWPCLGNnrwKB24VFI47oiY4IjYmlC_9xiYgVrOWp0EblhLVjydv57HOlk2me1L_Hf9LGbjrf3oxTRNqlOnOytelJjUqks5ijdMIIxDxWIIRTJAWcKyjBu8nG-HpT-bq7tGZSW07HjlAFULBcaZfxqWS7gdgm2n0uS_gGBLGhTJrx5BeBsrz7g9JSSRs3YDzd_6jx7RxujTBmbkpb323fxT97xrFTQ",
    "expires_in": 36000,
    "refresh_expires_in": 1800,
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICI0MjhhYTJmZS1jOGNmLTRkOWEtOTVjNi0yNTRlZTMyYzI4NDkifQ.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.gxHsfWLbp1JW8FvP16h6NztWjaT-i8Eeyt7KFqlb_lw",
    "token_type": "Bearer",
    "not-before-policy": 0,
    "session_state": "290ce1f2-e146-4001-b6da-2559d703cc6c"
}
```


## Create new User
### Requirements 
- Required role `Admin` or `SuperAdmin`
If you use role `admin` to create person - please ensure that the user email & company already existed in grakn
If you use role `superadmin`, you need to manually create a person with `email of superadmin` in grakn
  (Please read README.md in `data-lib` for this step) 
  
In local environment, after you create a new person by using this API , you need to activate it manually by:
- Access to keycloak console and go to your realm (`master`), on left side menu bar , choose `Users`
- Click on `View all users` & edit on the user you just created.
- Enable 2 toggle: `User Enabled` & `Email verified`
- Remove all values in `Required User Actions` and click save
- Switch to tabs `Credentials` -> set password for your account -> toggle off `Temporary` -> click `Save`
- Now you are ready to login with your created account.

*Important Notes*
* For now, one system should have only one `SuperAdmin` account
* A new created SuperAdmin account must have corresponding `Person` record in grakn (Please read through `datalib` README.md)

### Sample Request
#### Header
`X-Tenant-Id`: `master`
#### Sample Request: ``GET`` `http://localhost:8080/company/cd062179-eff5-402a-8cab-8f36e3fb4e13/users`

#### Request Body

```json
{
  "username": "<EMAIL>",
  "firstName": "Long",
  "lastName": "Nguyen",
  "description": "This is test user created",
  "properties": {
    "dob": "15/05/1994"
  }
}
```
### Sample Success Response

```json
{
  "createdAt": "2022-03-03T09:42:59",
  "disabled": false,
  "id": "0fa46ab9-837c-4fb3-ba57-90c6ebb1e88b",
  "updatedAt": "2022-03-03T09:42:59",
  "permissions": {
    "read": true,
    "update": true,
    "delete": true
  },
  "properties": {
    "dob": "15/05/1994",
    "name": "Long Nguyen",
    "type": "Person",
    "email": "<EMAIL>"
  },
  "relations": [
    {
      "entity": {
        "createdAt": "2022-03-03T09:35:14",
        "disabled": false,
        "id": "e5823cc8-b837-4bd7-94ec-e282ca0d2db8",
        "updatedAt": "2022-03-03T09:35:14",
        "permissions": {
          "read": true,
          "update": true,
          "delete": true
        },
        "properties": {
          "name": "CompanyA",
          "description": "CompanyA",
          "type": "InternalCompany"
        }
      },
      "relationName": "WORKS_FOR"
    }
  ]
}
```

### Sample Bad Request Response
```json
{
  "statusCode": 400,
  "errorMessage": "userEmail are already existed"
}
```
## HEALTH CHECK
{{auth-service-uri}}/health-ui/
- Check readiness of service ( service ready to receive request) using endpoint: {{auth-service-uri}}/health/ready
- Check status of service: {{auth-service-uri}}/health/live
- Startup health check {{auth-service-uri}}/health/started