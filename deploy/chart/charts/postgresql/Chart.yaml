annotations:
  category: Database
apiVersion: v2
appVersion: 14.4.0
dependencies:
- name: common
  repository: https://charts.bitnami.com/bitnami
  version: 1.x.x
description: PostgreSQL (Postgres) is an open source object-relational database known
  for reliability and data integrity. ACID-compliant, it supports foreign keys, joins,
  views, triggers and stored procedures.
home: https://github.com/bitnami/charts/tree/master/bitnami/postgresql
icon: https://bitnami.com/assets/stacks/postgresql/img/postgresql-stack-220x234.png
keywords:
- postgresql
- postgres
- database
- sql
- replication
- cluster
maintainers:
- name: Bitnami
  url: https://github.com/bitnami/charts
- email: <EMAIL>
  name: desaintmartin
name: postgresql
sources:
- https://github.com/bitnami/bitnami-docker-postgresql
- https://www.postgresql.org/
version: 11.6.18
