{{- if eq .Values.architecture "replication" }}
apiVersion: v1
kind: Service
metadata:
  name: {{ include "postgresql.readReplica.fullname" . }}
  namespace: {{ .Release.Namespace | quote }}
  labels: {{- include "common.labels.standard" . | nindent 4 }}
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonLabels "context" $ ) | nindent 4 }}
    {{- end }}
    app.kubernetes.io/component: read
  annotations:
    {{- if .Values.commonAnnotations }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
    {{- end }}
    {{- if .Values.readReplicas.service.annotations }}
    {{- include "common.tplvalues.render" (dict "value" .Values.readReplicas.service.annotations "context" $) | nindent 4 }}
    {{- end }}
spec:
  type: {{ .Values.readReplicas.service.type }}
  {{- if or (eq .Values.readReplicas.service.type "LoadBalancer") (eq .Values.readReplicas.service.type "NodePort") }}
  externalTrafficPolicy: {{ .Values.readReplicas.service.externalTrafficPolicy | quote }}
  {{- end }}
  {{- if and (eq .Values.readReplicas.service.type "LoadBalancer") (not (empty .Values.readReplicas.service.loadBalancerSourceRanges)) }}
  loadBalancerSourceRanges: {{ .Values.readReplicas.service.loadBalancerSourceRanges }}
  {{- end }}
  {{- if and (eq .Values.readReplicas.service.type "LoadBalancer") (not (empty .Values.readReplicas.service.loadBalancerIP)) }}
  loadBalancerIP: {{ .Values.readReplicas.service.loadBalancerIP }}
  {{- end }}
  {{- if and .Values.readReplicas.service.clusterIP (eq .Values.readReplicas.service.type "ClusterIP") }}
  clusterIP: {{ .Values.readReplicas.service.clusterIP }}
  {{- end }}
  {{- if .Values.readReplicas.service.sessionAffinity }}
  sessionAffinity: {{ .Values.readReplicas.service.sessionAffinity }}
  {{- end }}
  {{- if .Values.readReplicas.service.sessionAffinityConfig }}
  sessionAffinityConfig: {{- include "common.tplvalues.render" (dict "value" .Values.readReplicas.service.sessionAffinityConfig "context" $) | nindent 4 }}
  {{- end }}
  ports:
    - name: tcp-postgresql
      port: {{ include "postgresql.readReplica.service.port" . }}
      targetPort: tcp-postgresql
      {{- if and (or (eq .Values.readReplicas.service.type "NodePort") (eq .Values.readReplicas.service.type "LoadBalancer")) (not (empty .Values.readReplicas.service.nodePorts.postgresql)) }}
      nodePort: {{ .Values.readReplicas.service.nodePorts.postgresql }}
      {{- else if eq .Values.readReplicas.service.type "ClusterIP" }}
      nodePort: null
      {{- end }}
    {{- if .Values.readReplicas.service.extraPorts }}
    {{- include "common.tplvalues.render" (dict "value" .Values.readReplicas.service.extraPorts "context" $) | nindent 4 }}
    {{- end }}
  selector: {{- include "common.labels.matchLabels" . | nindent 4 }}
    app.kubernetes.io/component: read
{{- end }}
