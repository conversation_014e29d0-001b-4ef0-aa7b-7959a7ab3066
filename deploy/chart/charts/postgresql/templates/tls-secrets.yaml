{{- if (include "postgresql.createTlsSecret" . ) }}
{{- $ca := genCA "postgresql-ca" 365 }}
{{- $fullname := include "common.names.fullname" . }}
{{- $releaseNamespace := .Release.Namespace }}
{{- $clusterDomain := .Values.clusterDomain }}
{{- $primaryHeadlessServiceName := include "postgresql.primary.svc.headless" . }}
{{- $readHeadlessServiceName := include "postgresql.readReplica.svc.headless" . }}
{{- $altNames := list (printf "*.%s.%s.svc.%s" $fullname $releaseNamespace $clusterDomain) (printf "%s.%s.svc.%s" $fullname $releaseNamespace $clusterDomain) (printf "*.%s.%s.svc.%s" $primaryHeadlessServiceName $releaseNamespace $clusterDomain) (printf "%s.%s.svc.%s" $primaryHeadlessServiceName $releaseNamespace $clusterDomain) (printf "*.%s.%s.svc.%s" $readHeadlessServiceName $releaseNamespace $clusterDomain) (printf "%s.%s.svc.%s" $readHeadlessServiceName $releaseNamespace $clusterDomain) $fullname }}
{{- $crt := genSignedCert $fullname nil $altNames 365 $ca }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ printf "%s-crt" (include "common.names.fullname" .) }}
  namespace: {{ .Release.Namespace | quote }}
  labels: {{- include "common.labels.standard" . | nindent 4 }}
    {{- if .Values.commonLabels }}
    {{- include "common.tplvalues.render" ( dict "value" .Values.commonLabels "context" $ ) | nindent 4 }}
    {{- end }}
  {{- if .Values.commonAnnotations }}
  annotations: {{- include "common.tplvalues.render" ( dict "value" .Values.commonAnnotations "context" $ ) | nindent 4 }}
  {{- end }}
type: kubernetes.io/tls
data:
  ca.crt: {{ $ca.Cert | b64enc | quote }}
  tls.crt: {{ $crt.Cert | b64enc | quote }}
  tls.key: {{ $crt.Key | b64enc | quote }}
{{- end }}
