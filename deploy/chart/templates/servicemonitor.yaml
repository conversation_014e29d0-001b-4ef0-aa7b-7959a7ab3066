{{- if .Values.serviceMonitor.enabled }}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: {{ include "auth-mservice.fullname" . }}
  labels:
    {{- include "auth-mservice.labels" . | nindent 4 }}
spec:
  jobLabel: {{ include "auth-mservice.fullname" . }}
  endpoints:
    - port: http
      path: {{ .Values.serviceMonitor.path }}
      interval: {{ .Values.serviceMonitor.interval }}
  selector:
    matchLabels: 
      {{- include "auth-mservice.selectorLabels" . | nindent 6 }}
  namespaceSelector:
    matchNames:
      - {{ .Release.Namespace | quote }}      
{{- end }}