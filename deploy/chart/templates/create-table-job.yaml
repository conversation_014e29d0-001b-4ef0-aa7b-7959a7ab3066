{{- if .Values.job.createTable.enabled }}
apiVersion: batch/v1
kind: Job
metadata:
  name: {{ include "auth-mservice.fullname" . }}-create-table-{{ .Values.image.tag }}
  labels:
    {{- include "auth-mservice.labels" . | nindent 4 }}
  {{- if not .Values.postgresql.enabled }}
  annotations:
    "helm.sh/hook": pre-install,pre-upgrade
  {{- end }}
spec:
  ttlSecondsAfterFinished: 3600
  template:
    spec:
      containers:
      - name: postgres
        image: postgres:alpine
        command:
          - /bin/sh
          - -c
          - |
            PGPASSWORD=$(DB_AUTH_PASSWORD) psql -h $(DB_AUTH_HOST) -p 5432 -U $(DB_AUTH_USERNAME) -d $(DB_AUTH_NAME) -c 'CREATE TABLE IF NOT EXISTS tenant_configuration (
                  id varchar(255) NOT NULL,
                  created_by varchar(255) NULL,
                  created_date timestamp NULL,
                  "key" varchar(255) NULL,
                  tenant_id varchar(255) NOT NULL,
                  updated_by varchar(255) NULL,
                  updated_date timestamp NULL,
                  value varchar(255) NULL,
                  CONSTRAINT tenant_configuration_pkey PRIMARY KEY (id)
              );'
        env:
          {{- if .Values.postgresql.enabled }}
          - name: DB_AUTH_HOST
            value: "{{ .Release.Name }}-postgresql"
          {{- end }}
          {{- range $key, $value := .Values.variables }}
          - name: {{ $key }}
            value: {{ $value | quote }}
          {{- end }}
        {{- if .Values.secrets }}  
        envFrom:
          - secretRef:
              name: {{ include "auth-mservice.fullname" . }}
        {{- end }}
      restartPolicy: Never
  backoffLimit: 4
{{- end }}