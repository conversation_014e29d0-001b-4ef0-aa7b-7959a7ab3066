apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "auth-mservice.fullname" . }}
  labels:
    {{- include "auth-mservice.labels" . | nindent 4 }}
spec:
  {{- if not .Values.autoscaling.enabled }}
  replicas: {{ .Values.replicaCount }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "auth-mservice.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "auth-mservice.selectorLabels" . | nindent 8 }}
    spec:
      {{- if .Values.image.pullSecrets }}
      imagePullSecrets: {{- .Values.image.pullSecrets }}
      {{- end }}
      serviceAccountName: {{ include "auth-mservice.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.containerPort }}
              protocol: TCP
          env:
            {{- if .Values.postgresql.enabled }}
            - name: DB_AUTH_URL
              value: "vertx-reactive:postgresql://{{ .Release.Name }}-postgresql:5432/authdb"
            {{- end }}
            {{- range $key, $value := .Values.variables }}
            - name: {{ $key }}
              value: {{ $value | quote }}
            {{- end }}
          {{- if .Values.secrets }} 
          envFrom:
            - secretRef:
                name: {{ include "auth-mservice.fullname" . }}
          {{- end }}
          {{- if .Values.livenessProbe.enabled }}
          livenessProbe:
            httpGet:
              path: {{ default "/" .Values.livenessProbe.path }}
              port: http
            initialDelaySeconds: {{ default 5 .Values.livenessProbe.initialDelaySeconds }}
            periodSeconds: {{ default 5 .Values.livenessProbe.periodSeconds }}              
          {{- end }}   
          {{- if .Values.readinessProbe.enabled }}          
          readinessProbe:
            httpGet:
              path: {{ default "/" .Values.readinessProbe.path }}
              port: http
            initialDelaySeconds: {{ default 5 .Values.readinessProbe.initialDelaySeconds }}
            periodSeconds: {{ default 5 .Values.readinessProbe.periodSeconds }}
          {{- end }}           
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
