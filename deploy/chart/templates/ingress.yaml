{{- if .Values.ingress.enabled -}}
{{- $fullName := include "auth-mservice.fullname" . -}}
{{- $ingressPaths := .Values.ingress.paths -}}
{{- $sslredirect := .Values.ingress.awsSslRedirect }}
apiVersion: {{ include "auth-mservice.ingress.apiVersion" . }}
kind: Ingress
metadata:
  name: {{ $fullName }}
  labels:
    {{- include "auth-mservice.labels" . | nindent 4 }}
  {{- with .Values.ingress.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  {{- if and .Values.ingress.ingressClassName (eq (include "auth-mservice.ingress.apiVersion" $) "networking.k8s.io/v1") }}
  ingressClassName: {{ .Values.ingress.ingressClassName }}
  {{- end }}
  {{- if .Values.ingress.tls }}
  tls:
    {{- range .Values.ingress.tls }}
    - hosts:
      {{- range .hosts }}
        - {{ . | quote }}
      {{- end }}
      secretName: {{ .secretName }}
    {{- end }}
  {{- end }}
  rules:
    {{- range .Values.ingress.hosts }}
    - host: {{ . | quote }}
      http:
        paths:
          {{- if $sslredirect }}
          - path: /*
            pathType: "ImplementationSpecific"
            backend:
              service:
                name: ssl-redirect
                port:
                  name: use-annotation  
          {{- end }}
	        {{- range $ingressPaths }}
          - path: {{ . }}
            {{- if eq (include "auth-mservice.ingress.apiVersion" $) "networking.k8s.io/v1" }}
            pathType: "ImplementationSpecific"
            {{- end }}
            backend:
              {{- if eq (include "auth-mservice.ingress.apiVersion" $) "networking.k8s.io/v1" }}
              service:
                name: {{ $fullName }}
                port:
                  name: http
              {{ else }}
              serviceName: {{ $fullName }}
              servicePort: http
              {{- end }}            
	        {{- end }}
    {{- end }}
{{- end }}