service:
  type: NodePort

affinity:
  nodeAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      nodeSelectorTerms:
      - matchExpressions:
        - key: Environment
          operator: In
          values:
          - stag
  podAntiAffinity:
    preferredDuringSchedulingIgnoredDuringExecution:
    - podAffinityTerm:
        labelSelector:
          matchExpressions:
          - key: app.kubernetes.io/name
            operator: In
            values:
            - auth-mservice
        topologyKey: kubernetes.io/hostname
      weight: 100

livenessProbe:
  enabled: true
  path: "/health/live"
  initialDelaySeconds: 300
  periodSeconds: 10

readinessProbe:
  enabled: true
  path: "/health/ready"
  initialDelaySeconds: 5
  periodSeconds: 5

ingress:
  enabled: true
  annotations:
    kubernetes.io/ingress.class: alb
    alb.ingress.kubernetes.io/group.name: tripudiotech-dev
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}]'
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/tags: Environment=Tripudiotech-Dev
  paths:
    - /*

variables:
  COOKIE_DOMAIN: glideyoke.com
  DB_NAME: neo4j
  DB_SERVER: neo4j://neo4j-stag-headless.neo4j-stag:7687
  DB_TYPE: neo4j
  DB_USERNAME: neo4j
  DEFAULT_TENANT_ID: master
  ENTITY_SERVICE_URL: http://entity-mservice.tripudiotech-dev
  KEYCLOAK_SERVER_URL: https://identity-stage.glideyoke.com
  LANDING_PAGE_URL: https://<tenant>.glideyoke.com/
  LOGGING_SEVERITY_LEVEL: INFO
  MASTER_DOMAIN: ui-stage
  QUARKUS_OIDC__UI-QA-STAGE__AUTH_SERVER_URL: https://identity-stage.glideyoke.com/auth
  QUARKUS_OIDC__UI-QA-STAGE__LOGOUT_PATH: /authenticate/logout
  QUARKUS_OIDC__UI-QA-STAGE__LOGOUT_POST_LOGOUT_PATH: /authenticate/post-logout
  QUARKUS_OIDC__UI-QA-STAGE__TENANT_ID: ui-qa-stage
  QUARKUS_OIDC_AUTH_SERVER_URL: https://identity-stage.glideyoke.com/auth
  QUARKUS_PORT: "8080"
  quarkus.oidc.auth-server-url: https://identity-stage.glideyoke.com/auth
  quarkus.oidc.ui-qa-stage.auth-server-url: https://identity-stage.glideyoke.com/auth
  DB_AUTH_USERNAME: auth
  DB_AUTH_NAME: authdb
  DB_AUTH_URL: vertx-reactive:postgresql://auth-mservice-postgresql.tripudiotech-dev:5432/authdb
  TRIGGER_VERIFY_EMAIL: false
  TRIGGER_NOTIFY_EMAIL: false
  # DB_AUTH_HOST: only set if use external db
  CUSTOM_SMTP_USERNAME: AKIA3DZPVYJMIOLSNO7R
  INTERNAL_CLIENT_ID: glide-service-admin