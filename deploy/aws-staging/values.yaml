service:
  type: NodePort

resources:
  requests:
    cpu: 100m
    memory: 500Mi

affinity:
  nodeAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      nodeSelectorTerms:
      - matchExpressions:
        - key: Environment
          operator: In
          values:
          - stag
  podAntiAffinity:
    preferredDuringSchedulingIgnoredDuringExecution:
    - podAffinityTerm:
        labelSelector:
          matchExpressions:
          - key: app.kubernetes.io/name
            operator: In
            values:
            - auth-mservice
        topologyKey: kubernetes.io/hostname
      weight: 100

livenessProbe:
  enabled: true
  path: "/health/live"
  initialDelaySeconds: 300
  periodSeconds: 10

readinessProbe:
  enabled: true
  path: "/health/ready"
  initialDelaySeconds: 5
  periodSeconds: 5

serviceMonitor:
  enabled: true
  path: /q/metrics
  interval: 30s

ingress:
  enabled: true
  awsSslRedirect: true
  annotations:
    kubernetes.io/ingress.class: alb
    alb.ingress.kubernetes.io/actions.ssl-redirect: '{"Type": "redirect", "RedirectConfig":
      { "Protocol": "HTTPS", "Port": "443", "StatusCode": "HTTP_301"}}'
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:us-east-2:764067299928:certificate/a5a60cc2-aeb0-4a0f-9ff5-7bd93175f4a6
    alb.ingress.kubernetes.io/group.name: tripudiotech-dev
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS":443}]'
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/tags: Environment=Tripudiotech-Dev
  paths:
    - /*
  hosts:
    - auth-stage.glideyoke.com

variables:
  COOKIE_DOMAIN: glideyoke.com
  DB_NAME: neo4j
  DB_SERVER: neo4j://neo4j-stag-headless.neo4j-stag:7687
  DB_TYPE: neo4j
  DB_USERNAME: neo4j
  DEFAULT_TENANT_ID: master
  ENTITY_SERVICE_URL: http://entity-mservice
  KEYCLOAK_SERVER_URL: https://identity-stage.glideyoke.com
  LANDING_PAGE_URL: https://<tenant>.glideyoke.com/
  LOGGING_SEVERITY_LEVEL: INFO
  MASTER_DOMAIN: ui-stage
  QUARKUS_OIDC__UI-QA-STAGE__AUTH_SERVER_URL: https://identity-stage.glideyoke.com/auth
  QUARKUS_OIDC__UI-QA-STAGE__LOGOUT_PATH: /authenticate/logout
  QUARKUS_OIDC__UI-QA-STAGE__LOGOUT_POST_LOGOUT_PATH: /authenticate/post-logout
  QUARKUS_OIDC__UI-QA-STAGE__TENANT_ID: ui-qa-stage
  QUARKUS_OIDC_AUTH_SERVER_URL: https://identity-stage.glideyoke.com/auth
  QUARKUS_PORT: "8080"
  quarkus.oidc.auth-server-url: https://identity-stage.glideyoke.com/auth
  quarkus.oidc.ui-qa-stage.auth-server-url: https://identity-stage.glideyoke.com/auth
  DB_AUTH_USERNAME: auth
  DB_AUTH_NAME: authdb
  TRIGGER_VERIFY_EMAIL: true
  TRIGGER_NOTIFY_EMAIL: true
  # DB_AUTH_URL: only set if use external db
  # DB_AUTH_HOST: only set if use external db
  CUSTOM_SMTP_USERNAME: AKIA3DZPVYJMIOLSNO7R
  INTERNAL_CLIENT_ID: glide-service-admin

postgresql:
  enabled: true
  global:
    storageClass: gp3e
    postgresql:
      auth:
        username: auth
        database: authdb
  architecture: standalone
  primary:
    resources:
      requests:
        memory: 128Mi
        cpu: 50m
    persistence:
      size: 3Gi
    nodeSelector:
      Environment: stag