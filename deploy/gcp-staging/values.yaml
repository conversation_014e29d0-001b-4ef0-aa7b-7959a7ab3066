image:
  repository: us-east1-docker.pkg.dev/level-ruler-447003-u3/glideyoke-registry/auth-mservice

resources:
  requests:
    cpu: 100m
    memory: 500Mi

affinity:
  nodeAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      nodeSelectorTerms:
      - matchExpressions:
        - key: app-type
          operator: In
          values:
          - stateless
  podAntiAffinity:
    preferredDuringSchedulingIgnoredDuringExecution:
    - podAffinityTerm:
        labelSelector:
          matchExpressions:
          - key: app.kubernetes.io/name
            operator: In
            values:
            - auth-mservice
        topologyKey: kubernetes.io/hostname
      weight: 100

livenessProbe:
  enabled: true
  path: "/health/live"
  initialDelaySeconds: 300
  periodSeconds: 10

readinessProbe:
  enabled: true
  path: "/health/ready"
  initialDelaySeconds: 5
  periodSeconds: 5

ingress:
  enabled: true
  ingressClassName: nginx
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt
  paths:
    - /
  hosts:
    - auth-stage.gcp.glideyoke.com
  tls:
   - secretName: auth-mservice-tls
     hosts:
       - auth-stage.gcp.glideyoke.com

variables:
  COOKIE_DOMAIN: glideyoke.com
  DB_NAME: neo4j
  DB_SERVER: neo4j://neo4j-stag-headless.neo4j-stag:7687
  DB_TYPE: neo4j
  DB_USERNAME: neo4j
  DEFAULT_TENANT_ID: master
  ENTITY_SERVICE_URL: http://entity-mservice
  KEYCLOAK_SERVER_URL: https://identity-stage.gcp.glideyoke.com
  LANDING_PAGE_URL: https://<tenant>.glideyoke.com/
  LOGGING_SEVERITY_LEVEL: INFO
  MASTER_DOMAIN: ui-stage
  QUARKUS_OIDC__UI-QA-STAGE__AUTH_SERVER_URL: https://identity-stage.gcp.glideyoke.com/auth
  QUARKUS_OIDC__UI-QA-STAGE__LOGOUT_PATH: /authenticate/logout
  QUARKUS_OIDC__UI-QA-STAGE__LOGOUT_POST_LOGOUT_PATH: /authenticate/post-logout
  QUARKUS_OIDC__UI-QA-STAGE__TENANT_ID: ui-qa-stage
  QUARKUS_OIDC_AUTH_SERVER_URL: https://identity-stage.gcp.glideyoke.com/auth
  QUARKUS_PORT: "8080"
  quarkus.oidc.auth-server-url: https://identity-stage.gcp.glideyoke.com/auth
  quarkus.oidc.ui-qa-stage.auth-server-url: https://identity-stage.gcp.glideyoke.com/auth
  DB_AUTH_USERNAME: auth
  DB_AUTH_NAME: authdb
  DB_AUTH_URL: "vertx-reactive:postgresql://postgres-db.gcp.glideyoke.com:5432/authdb"
  DB_AUTH_HOST: postgres-db.gcp.glideyoke.com
  CUSTOM_SMTP_USERNAME: AKIA3DZPVYJMIOLSNO7R
  INTERNAL_CLIENT_ID: glide-service-admin
  TRIGGER_VERIFY_EMAIL: true
  TRIGGER_NOTIFY_EMAIL: true

job:
  createTable:
    enabled: false