[{"id": "master", "realm": "master", "displayName": "Keycloak", "displayNameHtml": "<div class=\"kc-logo-text\"><span>Keycloak</span></div>", "notBefore": 1641341933, "revokeRefreshToken": false, "refreshTokenMaxReuse": 0, "accessTokenLifespan": 86400000, "accessTokenLifespanForImplicitFlow": 900, "ssoSessionIdleTimeout": 1800, "ssoSessionMaxLifespan": 36000, "ssoSessionIdleTimeoutRememberMe": 0, "ssoSessionMaxLifespanRememberMe": 0, "offlineSessionIdleTimeout": 2592000, "offlineSessionMaxLifespanEnabled": false, "offlineSessionMaxLifespan": 5184000, "clientSessionIdleTimeout": 0, "clientSessionMaxLifespan": 0, "clientOfflineSessionIdleTimeout": 0, "clientOfflineSessionMaxLifespan": 0, "accessCodeLifespan": 60, "accessCodeLifespanUserAction": 300, "accessCodeLifespanLogin": 1800, "actionTokenGeneratedByAdminLifespan": 43200, "actionTokenGeneratedByUserLifespan": 300, "enabled": true, "sslRequired": "external", "registrationAllowed": false, "registrationEmailAsUsername": false, "rememberMe": false, "verifyEmail": false, "loginWithEmailAllowed": true, "duplicateEmailsAllowed": false, "resetPasswordAllowed": false, "editUsernameAllowed": true, "bruteForceProtected": false, "permanentLockout": false, "maxFailureWaitSeconds": 900, "minimumQuickLoginWaitSeconds": 60, "waitIncrementSeconds": 60, "quickLoginCheckMilliSeconds": 1000, "maxDeltaTimeSeconds": 43200, "failureFactor": 30, "roles": {"realm": [{"id": "dec9b0f2-45f4-40aa-8d3c-c45682154ed3", "name": "create-realm", "description": "${role_create-realm}", "composite": false, "clientRole": false, "containerId": "master", "attributes": {}}, {"id": "bc14d4f2-311e-40ad-a884-a858de76bdce", "name": "confidential", "composite": false, "clientRole": false, "containerId": "master", "attributes": {}}, {"id": "ded5e5d5-b007-40f4-ac8a-f3af86192151", "name": "admin", "description": "${role_admin}", "composite": true, "composites": {"realm": ["create-realm"], "client": {"master-realm": ["impersonation", "view-authorization", "manage-events", "query-clients", "manage-users", "query-realms", "view-identity-providers", "create-client", "manage-clients", "view-clients", "manage-realm", "view-users", "manage-identity-providers", "query-users", "manage-authorization", "query-groups", "view-events", "view-realm"]}}, "clientRole": false, "containerId": "master", "attributes": {}}, {"id": "8f4a3d62-e20c-4bc4-8346-2bc17ccc3565", "name": "SuperAdmin", "description": "- Only one super admin in our system", "composite": true, "composites": {"realm": ["create-realm", "offline_access", "admin", "uma_authorization"]}, "clientRole": false, "containerId": "master", "attributes": {}}, {"id": "03fd8215-0969-48d7-8d84-37ace925479d", "name": "uma_authorization", "description": "${role_uma_authorization}", "composite": false, "clientRole": false, "containerId": "master", "attributes": {}}, {"id": "f6391804-61e1-4bc2-8a4d-4369a42de6b6", "name": "offline_access", "description": "${role_offline-access}", "composite": false, "clientRole": false, "containerId": "master", "attributes": {}}], "client": {"sp-services": [{"id": "446d0c56-4a53-492e-b70b-5918864843c3", "name": "uma_protection", "composite": false, "clientRole": true, "containerId": "89a08a25-2685-4ab1-a6cc-9cacd06735fa", "attributes": {}}], "security-admin-console": [], "admin-cli": [], "quarkus-test": [], "account-console": [], "broker": [{"id": "3dce74e0-92ec-49c4-992d-b7ff68c45872", "name": "read-token", "description": "${role_read-token}", "composite": false, "clientRole": true, "containerId": "9a5ff999-a7e4-4fb5-a05d-71dd3e59a232", "attributes": {}}], "master-realm": [{"id": "5f388ba1-c773-4b9f-a272-18ff4231c5e8", "name": "impersonation", "description": "${role_impersonation}", "composite": false, "clientRole": true, "containerId": "a08db8a9-900f-4adc-9738-b6e68b6bfa05", "attributes": {}}, {"id": "f82fde5a-ab52-442c-a011-fe05bb4aa813", "name": "view-authorization", "description": "${role_view-authorization}", "composite": false, "clientRole": true, "containerId": "a08db8a9-900f-4adc-9738-b6e68b6bfa05", "attributes": {}}, {"id": "ce14fce6-831e-45c5-812a-1079137b7a90", "name": "manage-events", "description": "${role_manage-events}", "composite": false, "clientRole": true, "containerId": "a08db8a9-900f-4adc-9738-b6e68b6bfa05", "attributes": {}}, {"id": "2e3ced35-4165-4fea-ad76-14434bd82247", "name": "query-clients", "description": "${role_query-clients}", "composite": false, "clientRole": true, "containerId": "a08db8a9-900f-4adc-9738-b6e68b6bfa05", "attributes": {}}, {"id": "1b80f896-ef59-48f6-af53-00b7fa5ff337", "name": "manage-users", "description": "${role_manage-users}", "composite": false, "clientRole": true, "containerId": "a08db8a9-900f-4adc-9738-b6e68b6bfa05", "attributes": {}}, {"id": "2f5f8df6-981e-4d6c-9c2f-c03676319b52", "name": "query-realms", "description": "${role_query-realms}", "composite": false, "clientRole": true, "containerId": "a08db8a9-900f-4adc-9738-b6e68b6bfa05", "attributes": {}}, {"id": "0477d69e-cb79-418b-ab83-54cf8c550556", "name": "view-identity-providers", "description": "${role_view-identity-providers}", "composite": false, "clientRole": true, "containerId": "a08db8a9-900f-4adc-9738-b6e68b6bfa05", "attributes": {}}, {"id": "cb774e7f-6115-4095-ab96-cd30561545a7", "name": "create-client", "description": "${role_create-client}", "composite": false, "clientRole": true, "containerId": "a08db8a9-900f-4adc-9738-b6e68b6bfa05", "attributes": {}}, {"id": "46134896-cd52-48e2-9989-085b46508edd", "name": "manage-clients", "description": "${role_manage-clients}", "composite": false, "clientRole": true, "containerId": "a08db8a9-900f-4adc-9738-b6e68b6bfa05", "attributes": {}}, {"id": "f8203777-bb3e-452d-9718-cbcaf0c651bd", "name": "view-clients", "description": "${role_view-clients}", "composite": true, "composites": {"client": {"master-realm": ["query-clients"]}}, "clientRole": true, "containerId": "a08db8a9-900f-4adc-9738-b6e68b6bfa05", "attributes": {}}, {"id": "742f03f2-a015-485c-b2dd-ee79bcde6863", "name": "manage-realm", "description": "${role_manage-realm}", "composite": false, "clientRole": true, "containerId": "a08db8a9-900f-4adc-9738-b6e68b6bfa05", "attributes": {}}, {"id": "feb7e213-cc43-4c05-a2cd-2c75278d7c4a", "name": "view-users", "description": "${role_view-users}", "composite": true, "composites": {"client": {"master-realm": ["query-users", "query-groups"]}}, "clientRole": true, "containerId": "a08db8a9-900f-4adc-9738-b6e68b6bfa05", "attributes": {}}, {"id": "cb54fe2a-cd0e-43d8-9ced-7e6dfe33695c", "name": "manage-identity-providers", "description": "${role_manage-identity-providers}", "composite": false, "clientRole": true, "containerId": "a08db8a9-900f-4adc-9738-b6e68b6bfa05", "attributes": {}}, {"id": "499547cd-3c60-4168-8ba3-8b8dd69063a6", "name": "query-users", "description": "${role_query-users}", "composite": false, "clientRole": true, "containerId": "a08db8a9-900f-4adc-9738-b6e68b6bfa05", "attributes": {}}, {"id": "f86256d7-2a50-4239-9016-f6cefb134f5b", "name": "manage-authorization", "description": "${role_manage-authorization}", "composite": false, "clientRole": true, "containerId": "a08db8a9-900f-4adc-9738-b6e68b6bfa05", "attributes": {}}, {"id": "054495c3-9ae2-49f1-8b9d-cfc724434d70", "name": "query-groups", "description": "${role_query-groups}", "composite": false, "clientRole": true, "containerId": "a08db8a9-900f-4adc-9738-b6e68b6bfa05", "attributes": {}}, {"id": "191aecfa-d83f-4dcf-813b-8fa5ee66f909", "name": "view-events", "description": "${role_view-events}", "composite": false, "clientRole": true, "containerId": "a08db8a9-900f-4adc-9738-b6e68b6bfa05", "attributes": {}}, {"id": "42131d21-ca9f-4b65-a024-e4cb2ea9aea6", "name": "view-realm", "description": "${role_view-realm}", "composite": false, "clientRole": true, "containerId": "a08db8a9-900f-4adc-9738-b6e68b6bfa05", "attributes": {}}], "account": [{"id": "74868a79-f793-48bd-8360-8fbcd8e86098", "name": "manage-account", "description": "${role_manage-account}", "composite": true, "composites": {"client": {"account": ["manage-account-links"]}}, "clientRole": true, "containerId": "9b816600-eb7b-437f-b87e-8f882e49b47a", "attributes": {}}, {"id": "86c4aead-7e69-45f0-9402-0fb771e1e8e1", "name": "manage-account-links", "description": "${role_manage-account-links}", "composite": false, "clientRole": true, "containerId": "9b816600-eb7b-437f-b87e-8f882e49b47a", "attributes": {}}, {"id": "9b28bf6f-14c5-47cf-968b-f6c6d7f3b892", "name": "manage-consent", "description": "${role_manage-consent}", "composite": true, "composites": {"client": {"account": ["view-consent"]}}, "clientRole": true, "containerId": "9b816600-eb7b-437f-b87e-8f882e49b47a", "attributes": {}}, {"id": "a1f0e396-7911-4aac-8168-949ff9996cba", "name": "delete-account", "description": "${role_delete-account}", "composite": false, "clientRole": true, "containerId": "9b816600-eb7b-437f-b87e-8f882e49b47a", "attributes": {}}, {"id": "c9cd9832-d5b9-43b0-97dc-b1dab6bb0138", "name": "view-applications", "description": "${role_view-applications}", "composite": false, "clientRole": true, "containerId": "9b816600-eb7b-437f-b87e-8f882e49b47a", "attributes": {}}, {"id": "7cd7de01-9b38-40f2-bcc8-d9fa6a55ff25", "name": "view-consent", "description": "${role_view-consent}", "composite": false, "clientRole": true, "containerId": "9b816600-eb7b-437f-b87e-8f882e49b47a", "attributes": {}}, {"id": "c3129b04-ffa5-4351-81b2-b958a647d17d", "name": "view-profile", "description": "${role_view-profile}", "composite": false, "clientRole": true, "containerId": "9b816600-eb7b-437f-b87e-8f882e49b47a", "attributes": {}}]}}, "groups": [], "defaultRoles": ["uma_authorization", "offline_access"], "requiredCredentials": ["password"], "otpPolicyType": "totp", "otpPolicyAlgorithm": "HmacSHA1", "otpPolicyInitialCounter": 0, "otpPolicyDigits": 6, "otpPolicyLookAheadWindow": 1, "otpPolicyPeriod": 30, "otpSupportedApplications": ["FreeOTP", "Google Authenticator"], "webAuthnPolicyRpEntityName": "keycloak", "webAuthnPolicySignatureAlgorithms": ["ES256"], "webAuthnPolicyRpId": "", "webAuthnPolicyAttestationConveyancePreference": "not specified", "webAuthnPolicyAuthenticatorAttachment": "not specified", "webAuthnPolicyRequireResidentKey": "not specified", "webAuthnPolicyUserVerificationRequirement": "not specified", "webAuthnPolicyCreateTimeout": 0, "webAuthnPolicyAvoidSameAuthenticatorRegister": false, "webAuthnPolicyAcceptableAaguids": [], "webAuthnPolicyPasswordlessRpEntityName": "keycloak", "webAuthnPolicyPasswordlessSignatureAlgorithms": ["ES256"], "webAuthnPolicyPasswordlessRpId": "", "webAuthnPolicyPasswordlessAttestationConveyancePreference": "not specified", "webAuthnPolicyPasswordlessAuthenticatorAttachment": "not specified", "webAuthnPolicyPasswordlessRequireResidentKey": "not specified", "webAuthnPolicyPasswordlessUserVerificationRequirement": "not specified", "webAuthnPolicyPasswordlessCreateTimeout": 0, "webAuthnPolicyPasswordlessAvoidSameAuthenticatorRegister": false, "webAuthnPolicyPasswordlessAcceptableAaguids": [], "users": [{"id": "30f19308-0c12-46fe-8d05-97c41d117394", "createdTimestamp": 1620507784301, "username": "admin", "enabled": true, "totp": false, "emailVerified": true, "firstName": "Admin First Name", "lastName": "Admin Last Name", "email": "<EMAIL>", "credentials": [{"id": "98976125-98f5-447a-abd2-1afcea7ab165", "type": "password", "createdDate": *************, "secretData": "{\"value\":\"KxpdXIJmzteawp0zqPbNIOCs+F/JPWeMeb2cPzph/LKm0RyqvfhWjjqZIKr2czmVxqzNk/lGaNu830p6K92JCA==\",\"salt\":\"r4MxfIAHUGr1/kanG+zLOw==\",\"additionalParameters\":{}}", "credentialData": "{\"hashIterations\":27500,\"algorithm\":\"pbkdf2-sha256\",\"additionalParameters\":{}}"}], "disableableCredentialTypes": [], "requiredActions": [], "realmRoles": ["admin", "uma_authorization", "offline_access"], "clientRoles": {"account": ["manage-account", "manage-consent", "view-profile", "delete-account", "view-applications"]}, "notBefore": 0, "groups": []}, {"id": "584dcdbb-a3b0-470d-ace0-320f276f3e6b", "createdTimestamp": *************, "username": "service-account-sp-services", "enabled": true, "totp": false, "emailVerified": false, "serviceAccountClientId": "sp-services", "credentials": [], "disableableCredentialTypes": [], "requiredActions": [], "realmRoles": ["uma_authorization", "offline_access"], "clientRoles": {"sp-services": ["uma_protection"], "account": ["manage-account", "view-profile"]}, "notBefore": 0, "groups": []}, {"id": "bb7552a0-c949-400c-a074-fe00250fb3af", "createdTimestamp": *************, "username": "superadmin", "enabled": true, "totp": false, "emailVerified": true, "firstName": "Super First Name", "lastName": "Super Admin Last Name", "email": "<EMAIL>", "credentials": [{"id": "34027a5d-f700-48b7-972e-28a88c4a7056", "type": "password", "createdDate": *************, "secretData": "{\"value\":\"WOnhUpIkaWaHtWtkySouGnQk4wUZOE0NXjIxEqVpqDNI+tQRgX3kAtHHHGEAfNUpUH2Qp7hKqTnGEcJ3tdJ4MA==\",\"salt\":\"AeHouz4mvciSt6CkeqIWvQ==\",\"additionalParameters\":{}}", "credentialData": "{\"hashIterations\":27500,\"algorithm\":\"pbkdf2-sha256\",\"additionalParameters\":{}}"}], "disableableCredentialTypes": [], "requiredActions": [], "realmRoles": ["SuperAdmin"], "clientRoles": {"account": ["manage-account", "view-profile"]}, "notBefore": 0, "groups": []}, {"id": "4c341805-595a-41aa-b0d5-7abd4cc4a105", "createdTimestamp": *************, "username": "<EMAIL>", "enabled": false, "totp": false, "emailVerified": false, "firstName": "New", "lastName": "Name", "email": "<EMAIL>", "attributes": {"companyId": ["3b46e9c2-e509-4080-b905-ee688d09b0d2"], "createdBy": ["admin"], "referenceId": ["ae002d82-684d-49c0-8590-ffb9f26e3e95"]}, "credentials": [{"id": "ee17b373-4dcf-460d-b974-18f130170865", "type": "password", "createdDate": *************, "secretData": "{\"value\":\"54J7TqXXPtXgJ6FT6121Cor+x1rKpjZufj8tTVAyGbBZDOBL2VaQ0e+BRdtoTJw76bFcV6f3hz/OjY5JeinGEA==\",\"salt\":\"/ZNPoahv9RnLo47DYGGxAw==\",\"additionalParameters\":{}}", "credentialData": "{\"hashIterations\":27500,\"algorithm\":\"pbkdf2-sha256\",\"additionalParameters\":{}}"}], "disableableCredentialTypes": [], "requiredActions": ["VERIFY_EMAIL", "UPDATE_PASSWORD"], "realmRoles": ["uma_authorization", "offline_access"], "clientRoles": {"account": ["manage-account", "view-profile"]}, "notBefore": 0, "groups": []}], "scopeMappings": [{"clientScope": "offline_access", "roles": ["offline_access"]}], "clientScopeMappings": {"account": [{"client": "account-console", "roles": ["manage-account"]}]}, "clients": [{"id": "9b816600-eb7b-437f-b87e-8f882e49b47a", "clientId": "account", "name": "${client_account}", "rootUrl": "${authBaseUrl}", "baseUrl": "/realms/master/account/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "799b3f6e-7a71-4a9b-a5f2-dde0e33854bb", "defaultRoles": ["manage-account", "view-profile"], "redirectUris": ["/realms/master/account/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "role_list", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "9b600f6d-46f1-4665-8e9c-bfeebc61b0b0", "clientId": "account-console", "name": "${client_account-console}", "rootUrl": "${authBaseUrl}", "baseUrl": "/realms/master/account/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "928cd09b-f081-467a-9134-89f20a36963d", "redirectUris": ["/realms/master/account/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"pkce.code.challenge.method": "S256"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "ba855852-f21c-42bb-9e54-aa96958e30c1", "name": "audience resolve", "protocol": "openid-connect", "protocolMapper": "oidc-audience-resolve-mapper", "consentRequired": false, "config": {}}], "defaultClientScopes": ["web-origins", "role_list", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "7d86f609-7ecf-43dc-8b58-cd5b4b5f6246", "clientId": "admin-cli", "name": "${client_admin-cli}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "9ada0f51-3391-4310-92bf-419ded7e5d9e", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": false, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "role_list", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "9a5ff999-a7e4-4fb5-a05d-71dd3e59a232", "clientId": "broker", "name": "${client_broker}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "1669ac85-5f0c-4464-b219-c4e5bf27a677", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "role_list", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "a08db8a9-900f-4adc-9738-b6e68b6bfa05", "clientId": "master-realm", "name": "master Realm", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "8cc8944e-07e4-47b0-9aed-e02c51a485ba", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "role_list", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "36ff2510-984a-4ffa-abc3-6a145597095b", "clientId": "quarkus-test", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "5255a3f8-06d5-4c0a-870b-0be8ae3163eb", "redirectUris": ["http://localhost:8080/*", "http://127.0.0.1:8080/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"saml.assertion.signature": "false", "saml.multivalued.roles": "false", "saml.force.post.binding": "false", "saml.encrypt": "false", "backchannel.logout.revoke.offline.tokens": "false", "saml.server.signature": "false", "saml.server.signature.keyinfo.ext": "false", "exclude.session.state.from.auth.response": "false", "backchannel.logout.session.required": "false", "client_credentials.use_refresh_token": "false", "saml_force_name_id_format": "false", "saml.client.signature": "false", "tls.client.certificate.bound.access.tokens": "false", "saml.authnstatement": "false", "display.on.consent.screen": "false", "saml.onetimeuse.condition": "false"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "defaultClientScopes": ["web-origins", "role_list", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "d8e7a0ea-c541-455d-ab79-161b53ca2767", "clientId": "security-admin-console", "name": "${client_security-admin-console}", "rootUrl": "${authAdminUrl}", "baseUrl": "/admin/master/console/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "fe6a059e-ee14-468f-b9c7-01c006a839db", "redirectUris": ["/admin/master/console/*"], "webOrigins": ["+"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"pkce.code.challenge.method": "S256"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "cca6e063-9197-466e-9b0e-fa233896075e", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String"}}], "defaultClientScopes": ["web-origins", "role_list", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "89a08a25-2685-4ab1-a6cc-9cacd06735fa", "clientId": "sp-services", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "2d143300-8ad0-47aa-855a-06988f51c441", "redirectUris": ["", "*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": true, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"saml.assertion.signature": "false", "access.token.lifespan": "3600", "saml.force.post.binding": "false", "saml.multivalued.roles": "false", "saml.encrypt": "false", "backchannel.logout.revoke.offline.tokens": "false", "saml.server.signature": "false", "saml.server.signature.keyinfo.ext": "false", "exclude.session.state.from.auth.response": "false", "backchannel.logout.session.required": "false", "client_credentials.use_refresh_token": "true", "saml_force_name_id_format": "false", "saml.client.signature": "false", "tls.client.certificate.bound.access.tokens": "false", "saml.authnstatement": "false", "display.on.consent.screen": "false", "saml.onetimeuse.condition": "false"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "protocolMappers": [{"id": "9fd4d88e-d051-44d1-b8f6-8285b7189924", "name": "Client IP Address", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientAddress", "userinfo.token.claim": "true", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientAddress", "jsonType.label": "String"}}, {"id": "af9ac467-aeb6-4916-88b1-d5a05b4a0a84", "name": "Client Host", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientHost", "userinfo.token.claim": "true", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientHost", "jsonType.label": "String"}}, {"id": "71218cd6-ad13-4927-aeaf-4658b4d40626", "name": "Client ID", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientId", "userinfo.token.claim": "true", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientId", "jsonType.label": "String"}}], "defaultClientScopes": ["web-origins", "role_list", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}], "clientScopes": [{"id": "34052087-7a3c-4163-9905-4890b5a005d6", "name": "offline_access", "description": "OpenID Connect built-in scope: offline_access", "protocol": "openid-connect", "attributes": {"consent.screen.text": "${offlineAccessScopeConsentText}", "display.on.consent.screen": "true"}}, {"id": "67d25283-444f-48b8-9eaa-dbeae37e1479", "name": "role_list", "description": "SAML role list", "protocol": "saml", "attributes": {"consent.screen.text": "${samlRoleListScopeConsentText}", "display.on.consent.screen": "true"}, "protocolMappers": [{"id": "6acb76cd-86da-4710-a5fe-c726487a1c00", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}]}, {"id": "4e10af2f-684f-4fc4-b560-fc52fe9572a0", "name": "profile", "description": "OpenID Connect built-in scope: profile", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${profileScopeConsentText}"}, "protocolMappers": [{"id": "46c5170a-f556-4cfc-9083-aa74ef9d116b", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String"}}, {"id": "083dc4a3-8ba7-4326-9126-58755f03bca7", "name": "nickname", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "nickname", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "nickname", "jsonType.label": "String"}}, {"id": "48e92106-c8bd-4859-81a4-267fc43607f7", "name": "updated at", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "updatedAt", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "updated_at", "jsonType.label": "String"}}, {"id": "a07eb351-e8f7-4106-b440-c8a6ef6f09b5", "name": "gender", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "gender", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "gender", "jsonType.label": "String"}}, {"id": "853d54fd-9436-403b-9e74-c6a698b08af3", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}, {"id": "c1ef9fae-e3ea-47af-8cfd-56845b0c5493", "name": "middle name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "middleName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "middle_name", "jsonType.label": "String"}}, {"id": "423cdd9d-13a6-4ccb-93b6-457f12606b02", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": false, "config": {"id.token.claim": "true", "access.token.claim": "true", "userinfo.token.claim": "true"}}, {"id": "c8cc3e46-133d-4cfd-b776-677f91c217b1", "name": "picture", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "picture", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "picture", "jsonType.label": "String"}}, {"id": "229b9f80-019a-488c-9c45-b972aeffc6b2", "name": "zoneinfo", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "zoneinfo", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "zoneinfo", "jsonType.label": "String"}}, {"id": "5fdc4df0-bd01-41d1-aa6e-bc019794e2c0", "name": "profile", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "profile", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "profile", "jsonType.label": "String"}}, {"id": "ad283854-0f62-45bc-bbbd-b8cf6144f280", "name": "website", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "website", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "website", "jsonType.label": "String"}}, {"id": "ffa3d6dd-f0c0-4fc0-bf40-b1cd0c17ee04", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}, {"id": "ba468c19-c13a-4c45-98ea-2498cb2c6d50", "name": "birthdate", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "birthdate", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "birthdate", "jsonType.label": "String"}}, {"id": "09292ab0-6904-45a0-905d-3059e2bab769", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}]}, {"id": "e24aec21-a957-40f1-86fa-f4c6221ae8a3", "name": "email", "description": "OpenID Connect built-in scope: email", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${emailScopeConsentText}"}, "protocolMappers": [{"id": "a187510f-9ce2-4f0b-a719-d9f6a7208577", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}, {"id": "c95bbfad-e06d-48ec-802c-e93de27aa13d", "name": "email verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "emailVerified", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email_verified", "jsonType.label": "boolean"}}]}, {"id": "76d5f499-4d8f-4eda-9e61-1a99bdbba43e", "name": "address", "description": "OpenID Connect built-in scope: address", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${addressScopeConsentText}"}, "protocolMappers": [{"id": "29e1bdd0-7fe6-4974-9977-120bed6bd59d", "name": "address", "protocol": "openid-connect", "protocolMapper": "oidc-address-mapper", "consentRequired": false, "config": {"user.attribute.formatted": "formatted", "user.attribute.country": "country", "user.attribute.postal_code": "postal_code", "userinfo.token.claim": "true", "user.attribute.street": "street", "id.token.claim": "true", "user.attribute.region": "region", "access.token.claim": "true", "user.attribute.locality": "locality"}}]}, {"id": "372611f9-b475-482e-a733-f973adc52bc8", "name": "phone", "description": "OpenID Connect built-in scope: phone", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${phoneScopeConsentText}"}, "protocolMappers": [{"id": "3c420072-8d0c-4111-b539-3c1d08971a9b", "name": "phone number", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "phoneNumber", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "phone_number", "jsonType.label": "String"}}, {"id": "900a9688-585d-44ae-947c-96e8a06a4432", "name": "phone number verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "phoneNumberVerified", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "phone_number_verified", "jsonType.label": "boolean"}}]}, {"id": "62065f75-fcba-45da-8c11-be89a1eaa818", "name": "roles", "description": "OpenID Connect scope for add user roles to the access token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "true", "consent.screen.text": "${rolesScopeConsentText}"}, "protocolMappers": [{"id": "1798b476-adce-40ef-b63d-c1db7e8b0765", "name": "client roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-client-role-mapper", "consentRequired": false, "config": {"user.attribute": "foo", "access.token.claim": "true", "claim.name": "resource_access.${client_id}.roles", "jsonType.label": "String", "multivalued": "true"}}, {"id": "813053ee-5593-4777-8026-a2508cf3df26", "name": "audience resolve", "protocol": "openid-connect", "protocolMapper": "oidc-audience-resolve-mapper", "consentRequired": false, "config": {}}, {"id": "01903b6d-5924-419b-a2bb-d2daa0bbffc8", "name": "realm roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"user.attribute": "foo", "access.token.claim": "true", "claim.name": "realm_access.roles", "jsonType.label": "String", "multivalued": "true"}}]}, {"id": "3c5f6a6e-855b-424e-b059-0de859aa049f", "name": "web-origins", "description": "OpenID Connect scope for add allowed web origins to the access token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "false", "consent.screen.text": ""}, "protocolMappers": [{"id": "5330474b-4160-4c0e-90c0-e9a7f686044e", "name": "allowed web origins", "protocol": "openid-connect", "protocolMapper": "oidc-allowed-origins-mapper", "consentRequired": false, "config": {}}]}, {"id": "1ae28dd2-0d94-45cd-a7c6-b66f624ba3ee", "name": "microprofile-jwt", "description": "Microprofile - JWT built-in scope", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "false"}, "protocolMappers": [{"id": "0dcef4eb-b53c-4fe1-9e09-cee3d2842799", "name": "upn", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "upn", "jsonType.label": "String"}}, {"id": "ebadc80b-6066-4c87-b086-8e271f77d404", "name": "groups", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"multivalued": "true", "userinfo.token.claim": "true", "user.attribute": "foo", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "groups", "jsonType.label": "String"}}]}], "defaultDefaultClientScopes": ["role_list", "profile", "email", "roles", "web-origins"], "defaultOptionalClientScopes": ["offline_access", "address", "phone", "microprofile-jwt"], "browserSecurityHeaders": {"contentSecurityPolicyReportOnly": "", "xContentTypeOptions": "nosniff", "xRobotsTag": "none", "xFrameOptions": "SAMEORIGIN", "contentSecurityPolicy": "frame-src 'self'; frame-ancestors 'self'; object-src 'none';", "xXSSProtection": "1; mode=block", "strictTransportSecurity": "max-age=31536000; includeSubDomains"}, "smtpServer": {}, "eventsEnabled": false, "eventsListeners": ["jboss-logging"], "enabledEventTypes": [], "adminEventsEnabled": false, "adminEventsDetailsEnabled": false, "identityProviders": [], "identityProviderMappers": [], "components": {"org.keycloak.services.clientregistration.policy.ClientRegistrationPolicy": [{"id": "d173bb47-95ec-4642-9afb-82626d332f94", "name": "Full Scope Disabled", "providerId": "scope", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "c86f5b39-be8a-452b-a1b5-f033183cad37", "name": "Allowed C<PERSON>s", "providerId": "allowed-client-templates", "subType": "authenticated", "subComponents": {}, "config": {"allow-default-scopes": ["true"]}}, {"id": "79c295ba-94b4-4912-9874-0678397fb00b", "name": "Trusted Hosts", "providerId": "trusted-hosts", "subType": "anonymous", "subComponents": {}, "config": {"host-sending-registration-request-must-match": ["true"], "client-uris-must-match": ["true"]}}, {"id": "0093d23d-5188-4d82-9ed0-84d152abed6c", "name": "Max Clients Limit", "providerId": "max-clients", "subType": "anonymous", "subComponents": {}, "config": {"max-clients": ["200"]}}, {"id": "52e9c835-7593-4dbf-9315-2bbd96643e86", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "anonymous", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["oidc-usermodel-attribute-mapper", "oidc-sha256-pairwise-sub-mapper", "oidc-full-name-mapper", "saml-user-property-mapper", "oidc-address-mapper", "oidc-usermodel-property-mapper", "saml-role-list-mapper", "saml-user-attribute-mapper"]}}, {"id": "79679720-4b45-4bf9-8844-3f18269a9da2", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "authenticated", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["oidc-usermodel-attribute-mapper", "oidc-address-mapper", "oidc-usermodel-property-mapper", "saml-role-list-mapper", "saml-user-attribute-mapper", "oidc-full-name-mapper", "saml-user-property-mapper", "oidc-sha256-pairwise-sub-mapper"]}}, {"id": "058ad0cb-a984-403c-9e4c-57218e23663e", "name": "Consent Required", "providerId": "consent-required", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "89a032a6-e1ad-4da2-9738-3b9a9b0e9b88", "name": "Allowed C<PERSON>s", "providerId": "allowed-client-templates", "subType": "anonymous", "subComponents": {}, "config": {"allow-default-scopes": ["true"]}}], "org.keycloak.keys.KeyProvider": [{"id": "e04d5f71-90aa-4752-828d-c3fe35551b2e", "name": "fallback-HS256", "providerId": "hmac-generated", "subComponents": {}, "config": {"kid": ["66f26022-c1fa-469c-b99e-0ab652fa72ea"], "secret": ["0tQtfKk5NlUclw_Lhw4jQfEJ094FbmkzWw9pep0ZWtZOut3fTkkkNPgrFht5HN3XIguAEN3NM2DSm2bJSmBfAw"], "priority": ["-100"], "algorithm": ["HS256"]}}, {"id": "40fb9f1e-60d2-4c36-bde2-e57ec6472370", "name": "fallback-RS256", "providerId": "rsa-generated", "subComponents": {}, "config": {"privateKey": ["MIIEowIBAAKCAQEAvk6MsuEJ2md6PoaQazaog/+8HKbNiOCOUZNTNyNmkX3zJqGbD++f94eQPY5L0VgCylhPpncnIDdo3d/NYrHYS45teNt7x8md1vxYY0FhUU6MM6GfjC7l3TBMmMXdiy2Owrl6QLDBOou0JPP1qhonpptCxxS38ErJX/CXmQfMME6VwoL1THNhKmNJtlQBA/ddRRvRA3ntkRzlkWVRXPHfyH0ide5YeMsy54ztyq0X26ng/r2lAuzSSq5JjPid+2ZE4q++wfYBLE0nHdfFGWkjw65pzAcdw88NtIvqrMNWVu5UKJ56R3ulurVcq2imxXbg/vLG1Llk4UYCdKN9jJrK4wIDAQABAoIBAHMZJOWHT6NFVFz+m9gMPc1f3ZDinYq8bbcOyJSfl4hFSctCkHsJ7ZES730bU+WwOjfRQ5Mlm4dTm9wdYKXhdDT2VmVzMaD64rjE9FxLjaVUQV100f9B2gp2DM2VVlG34xEIhtUbUjKrje+PkTQpD3QnkTLA6CKvyK164OVQHwHTaX2H3ZcNvyLU5CDdNnpow/id6u6RDSfbGYMiiNRt4BCt1Kz4IMiTVZdQKSAZv6vJNYTPy+9tUq05VzL8jjcuJDrvNs+O9LRlpTzd/A/2SGCGyi+wmpolmqApPuKT/Qk5yu4MWglaaOlQ/P+PX9YezjggMOFUedJmAQiHjkx+RfkCgYEA8ljudXomtyJt2hl96U0i6Zmdpy5doIFpNOytMtApFgW8OBz2HTihjJWjNEF43h5XUg48o+Va9xt7Jj4e7w85bDx8HpTyo8wH3h0N2VRv5ucjzqDynElGhfXsWNzOxQeePpmFo9lZU1vxh2tbAZPFZtOXhbzYpwN/Ytd5GkElBUcCgYEAyQcaczvygDA/VEgIU1RhM7A78jh+61smV0Yr2fdXJUnjRd+yk7C4/bUPjM1KoB/cwFW1VWsXPsdy0S2dqycGFDxjKafCtLUGVd8tPEoOuS2W57m+6GyP4Aq045/saAtbPmKH4UawPdiyoA9cAvNZwRDkk8XnJCOlcVxAsnAzC4UCgYBGG6IITCkNC6G52WwKZO03pN/tsEUXnLhhviIws/Ve0JPtq7srIunhMJzBDju4DhD2xPWX+E7Dc6jBnHD4eXAENs81TaOP7QNNpxwNhhbFNpKuQPNOcQg80rLn1iDe8OA/fcwiZUspCU0q/syZ/TS7FXUdFE+cqS9d2bXH+ZaLGwKBgEul5qgyiOEARvSaXres6x8eVJCj/e7nUBhLibjuQNcBZQsoUPUO7a5bckyT/F8hXlrqZeCM+iRfva9FsS108+6tgstsG/MQpB+waj9jxwKcS0tMNLEWGVY/8S4vQjEtBNsHDQcTKMM9RorA/OkHVmE20C1jrcWFvH5aLuZ3Hl5ZAoGBAMkxIRhx4nrgoW8K34rz0kjGshSlVOlcZNE/xpgPPBxSSrK0dUk69THZ/kXjemmgumV4wLgDNpO8XgPSh89UhKwEyIhRZojqpc6+pbcy5s0/8+s7REqUJB7JZt5VOyNtvHup1oKm2GV3P+8mT8U43Xx/ngefoCD+IvRB9ENKR57U"], "certificate": ["MIICmzCCAYMCBgF5Tcp7kzANBgkqhkiG9w0BAQsFADARMQ8wDQYDVQQDDAZtYXN0ZXIwHhcNMjEwNTA4MjEwMTI2WhcNMzEwNTA4MjEwMzA2WjARMQ8wDQYDVQQDDAZtYXN0ZXIwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQC+Toyy4QnaZ3o+hpBrNqiD/7wcps2I4I5Rk1M3I2aRffMmoZsP75/3h5A9jkvRWALKWE+mdycgN2jd381isdhLjm1423vHyZ3W/FhjQWFRTowzoZ+MLuXdMEyYxd2LLY7CuXpAsME6i7Qk8/WqGiemm0LHFLfwSslf8JeZB8wwTpXCgvVMc2EqY0m2VAED911FG9EDee2RHOWRZVFc8d/IfSJ17lh4yzLnjO3KrRfbqeD+vaUC7NJKrkmM+J37ZkTir77B9gEsTScd18UZaSPDrmnMBx3Dzw20i+qsw1ZW7lQonnpHe6W6tVyraKbFduD+8sbUuWThRgJ0o32MmsrjAgMBAAEwDQYJKoZIhvcNAQELBQADggEBAJi+KYVSsiJYv3u+tfRwa0SYzY3yoSucgMxrDbcnmy/+qsPNHt+f8ewHeERLEKNOynrQLo/LxLjBqcnVJMJLWarGyRlA4D0VQkrUrowRnQ8EgwjzpzAbPhPV8axoUue8K7RpPeaf93XCNmKiIERs9eWp+M+63ZWI3eX93pbLLyOA6zZJMpokVo5Iyg7j59IxEoNsJC33vh+qGti0P9oa2d73oKByYxIq60EYz7YpDt4t6da3OPkprb1kT3QxhmZ8kVAZAXdpU6R4uGdAQxvX2iieAO4YtrO04MeWBjUDN8Yr22I4pVBk5fGq6sj9uoOXeVU6ovA34KbMlzlX1wjibEU="], "priority": ["-100"], "algorithm": ["RS256"]}}, {"id": "f6575112-4856-404c-b72a-51b7cdfafa21", "name": "fallback-HS256", "providerId": "hmac-generated", "subComponents": {}, "config": {"kid": ["56222583-b956-4951-a0c2-eb56c2692333"], "secret": ["jsX6oJ_0l1Bt19liDx0Bpz6RT9TiydnQY9OnSU4kQehWgilA6nepAm6r7ZDoUUtkk7ZfOtaaSSnsy_nPqCvD1g"], "priority": ["-100"], "algorithm": ["HS256"]}}, {"id": "df786be1-6f4d-4bd9-859f-9ddc5d17aa46", "name": "fallback-RS256", "providerId": "rsa-generated", "subComponents": {}, "config": {"privateKey": ["MIIEowIBAAKCAQEAiCWwumjXYTotE27gIo90NLkU+sXMdxcmWQ5sBe3rmXofLWtfZrcXlxEdzTpbBslIAje77OsU88735UsVh/KQrQ/Wg6/AXKs6ko8cBHyhqtZP7scMUdfzsj05QGGV13rprcX48EaPbdTimvX/zMOhLaSJRVXe9zlIMaIzQLZizOz2GOLOAv2b9I/GMWFdlLf5biLd9Bf1zdeayATOyhiZ6It6slJ4uCmHuRF456ekaod1RWuRB4XoQW+iDrsH6mBI1iVGeYCtjuRaRNhkEykdOOXCiX/H1QyOnx4zUESisjugD5C4mcNflD1JXSSbuI9geMHdgg/y1LTvKY1KN577EQIDAQABAoIBAEINpzSJcRVm2D+5Z1zdJI//y8Hu/Ug6HwzOBnLJ/6gyq7WphlubMQLF9yN+0HL2bM1GpRJI651uKK9CQmv2r2KHcJfrSPSsznoUnI9AuF7eI2HBiKSclUtAkOo/bidhJsZ8ak6IrUWoJ1jvSO/bafNvBHpzMv3W+MO74gnfcwfgpG0xSSa7Y3gTzlj14mMWVF5JgJQcVaDbLsI3FWvid8S7IHZfPMaat32XEso7thckjOBU5kMojazY4TVxxqjS1ucjQuCBK5yxlX+Gb0LOzFGINRZj+0wF5pfyPKeO0qSvBBTL32LP2fW0bxn2CJYgHmRLpNUeyk9U/B0/bHTgqpECgYEA8LHRJFgDPimbTGV5SzMEIO9EZF87UnzP/QCCKJuPKoG80II36V63ge/DuZoc+LsoB/5bE4fGX9oopEpCTBRBRr1Zre8gnb0gz4PwdcXEP/nJOH1wGkAOZhqmlwiX5Z06oe/RynjrIj9PymJZQlsoCmm6YwoA97ia0OYHVoZUz9MCgYEAkM37volqbFJaGKB97ajnFrYWCcisQGeI5Du5dyubaJSctGrIw+0Do11MdLyxu6sJNo29oyWEuB7TtkDClGVsNN5jv+30JA+/2lsrgeOlu5bd4PsEGk6dU/rQpz9euDkXuTTGuO+LZDklwJAGS+bZqS1h5t2rV55YkZruoz3CnwsCgYEAuyW3dSfN1a6qJpub1QGkyyrfJZ/6Uyfi3Fhh28sgZS+dLQ6jBJx12N0hdRXEQw3akWSPUj822l6ZTSzEIb16B9PAgAI3fQ6KDXAMLraH4iqI75hw6qg1yeQc9cBjwtAev7zlhSXBxc4ubzFLL9IK7xXIK6uilvp9vcGtoPQvy6sCgYAeHimxPKWc+BfzLVZj2xCNgbvdtxZUovQu/Fkb9z7wppBdWiR908tA7EPdt+2DqhRVe2+pAkGzhf3k111hPiMeb+7xWY3A/mGaCgJnlrC+7qchhzQYQKeqcu15C84opP9RLy/yASjN5LD8dzhICzDUZjMJsKtVowKq7j2aSFs1dwKBgEE3zk8qLjYeExZ9H9GWHMFpdzOexwyEH5Gy7/vrb4Vezq+6tWqJzYHJSlSX0Qm3IMyiDFR/ZumZXaYBz8oFWrGADf4sogAPaSTo/9/E7CaRO5ECTRwRk34Gz1ugtsKGoFg7zQ+1xKEJOHmIBKFJuFJaSDu03D4jwri0i3MehKrf"], "certificate": ["MIICmzCCAYMCBgF5Tcp7lDANBgkqhkiG9w0BAQsFADARMQ8wDQYDVQQDDAZtYXN0ZXIwHhcNMjEwNTA4MjEwMTI2WhcNMzEwNTA4MjEwMzA2WjARMQ8wDQYDVQQDDAZtYXN0ZXIwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQCIJbC6aNdhOi0TbuAij3Q0uRT6xcx3FyZZDmwF7euZeh8ta19mtxeXER3NOlsGyUgCN7vs6xTzzvflSxWH8pCtD9aDr8BcqzqSjxwEfKGq1k/uxwxR1/OyPTlAYZXXeumtxfjwRo9t1OKa9f/Mw6EtpIlFVd73OUgxojNAtmLM7PYY4s4C/Zv0j8YxYV2Ut/luIt30F/XN15rIBM7KGJnoi3qyUni4KYe5EXjnp6Rqh3VFa5EHhehBb6IOuwfqYEjWJUZ5gK2O5FpE2GQTKR045cKJf8fVDI6fHjNQRKKyO6APkLiZw1+UPUldJJu4j2B4wd2CD/LUtO8pjUo3nvsRAgMBAAEwDQYJKoZIhvcNAQELBQADggEBAHbxPCexmzFUCru7TZ8UxZz20AgM5GgSxHbKrMvK+AZi8W5PM0BiUEl35bs6fJtD/v1uH9Do7zgf031pGliltIHK3GQ91a5LSpu2SMf/wFamih9PCUxJvneULWrC0VtCtBRkOwL2BEhHgc1SArra14BFMwd59Q7olUN+lsT/rGPmppVNwOzLDmjG14zlq9xTWZtLSe8R++v/3f65/iZ+3DcQ9BsnEVAbPAZ3WyOSzjzB0mZu1vxizyJ1Ah+cxtgCfIvkWi3BVE04gS+rIbvkIjqbDVgjbemvq42KPfve9v3H0c1aditkTWaeAWT4Mg1UxoVfxMWWErmUKHhJGcTh4Fw="], "priority": ["-100"], "algorithm": ["RS256"]}}, {"id": "56a006ee-358e-442b-b883-4953b2c7a260", "name": "fallback-HS256", "providerId": "hmac-generated", "subComponents": {}, "config": {"kid": ["e27741e3-82b6-47d6-93c8-07fc9eba3411"], "secret": ["E7GqaJL2i14J0FbHo66tFh9JCaIRVf2TBH1ZA0qA184rH3TKirkT6hD_DEe-Iu9PEIMVx6BLSQUaEJuMtC3VBw"], "priority": ["-100"], "algorithm": ["HS256"]}}, {"id": "e2a5842b-a03f-4171-a259-7e6e733a1355", "name": "fallback-RS256", "providerId": "rsa-generated", "subComponents": {}, "config": {"privateKey": ["MIIEowIBAAKCAQEAmgSVaN8JJAAXTq/PhxZECkssasLVXWV9p4ObAKY2QEKvDLecCn0AKrrnY7J1UZfbRhKIoQWZb0XG4RcwWRhrWJOrU/gK+7lzt0NVQHEUHW0g3EPQg7eJOti3ua7eRDqtfgRChrK9BhYectM0bKxRoSofruR9fvMVVVJJrPpjId+/lRm5u8+CY29cniIFIjkJRWle18EoiJCjvmv0Lao6NX47qztQ8+D39fsP2dvAN9hLLAgDLWWtuzoTaNs/kXd4+kG5uibNa5sFlqhOyQgJJ5twfFuiplY6Ak7+p76Kpmq/y5jbji229UTRJQG5+sLD4XuxgSg7HqxeYjKXAlG8WQIDAQABAoIBADObC5DrAn4MhcK4x9lkjYHBdG2FQMadhf/F6f7KsmzPqyWgs+kTvyWSIIvYNNUVA8Sz2xHQ9XtCLwQmp+2CcB1qxUJKXyu/FNW3+u2vteSyNwt2sXwg2BtemBjgTdsgqhcKSFwnsYxDhwpEWFF9X5CXs6v/JUmPG3vOKZ+K+gJUF5IP+6ipFb4HQF62XpBKWGLz9RXAPAEk+xMD68X4A+G36q2Q14+7hCGeuy8zNZFFLHcaoaHNIk0Zsf9hXtwTqMdTeKSf+J9UO1xGEMhVyFuMI6QvpGUB08ED1EolD4bp7jNFZo1Tz2ect/o0+EyT7LB2YJOcWMAPyUafgVOMp3ECgYEA2ekGmQLDgQ3LRV0sO4ipsjVfiWlTd3yu2xsuMKc9XTgN8mzdSl3XZvVTMPwBKqoiThjIjuFkOqO1f1ZyB6az0V0CjBHohBlDA/GABnjZgtQpfthDAQpiuxi5K26FJiaSUMpSzCf2XppSTZCN5YqDUs7KBQu6yOMq1vLaxhF0nMUCgYEAtPCGSijrwRXiCYtUgZp7YpzCW4avbDlrK/nuTgBNJ6zBXQ6HMF+mxwBRuzGT4W4zvF6ayW5OdjOsFibpzeqeStDmn7UJZwXhS7P+PiSG+mzOq494QX4IrmjKoffpVx2/upIDdaTW17QMdUDCeCuCVSuzKlrEMJqzHl4Re8cjwoUCgYBC73xyVdH9+2G8LdGfe+w8bH6rM+pKoscHUMMQeN61mrlWBTC4XjkDTEpAmrg1BcvB14ULtppWFOF0evZN+Vv07LASnJGiuivGs1ETK94MfOU+LGnwkOMg+4gtmiZWQLTjEjL6uS/JgXfUkZm0r6aFMjYF9MNsX7NSEYk4HDaIzQKBgQCqGxpVLCWAMH+CM5AuDDsFmZSLkZLobw7XmC+2+0eODjb3soncK4DSw/USxOhfXkibRmHFD8QKEkgtx0W1xmYstjqrWPrO9VM65awXZiXT12lxFv+aqs+kkHY4Uab4tMokr9M6zc8BmY7AyTJK4bS1N9FH929MtpPA7/UF5lEzNQKBgAqKCAc0hs0lhhCf3GLmoLlv5dft2HycBM2ToKlsVNzw9nSJ+8PqabLbBRz3YaK95DNLQaPD46T3C/3pdKFEi5tOic38Ock3kqoJpzzf5caIWOa0tdUDUbbOFu1Gs6Xd/44KO/FmKc70hRRD5sYR/qPZhqwTN5vPYzBZgJc3eT90"], "certificate": ["MIICmzCCAYMCBgF5Tcp9jzANBgkqhkiG9w0BAQsFADARMQ8wDQYDVQQDDAZtYXN0ZXIwHhcNMjEwNTA4MjEwMTI2WhcNMzEwNTA4MjEwMzA2WjARMQ8wDQYDVQQDDAZtYXN0ZXIwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQCaBJVo3wkkABdOr8+HFkQKSyxqwtVdZX2ng5sApjZAQq8Mt5wKfQAquudjsnVRl9tGEoihBZlvRcbhFzBZGGtYk6tT+Ar7uXO3Q1VAcRQdbSDcQ9CDt4k62Le5rt5EOq1+BEKGsr0GFh5y0zRsrFGhKh+u5H1+8xVVUkms+mMh37+VGbm7z4Jjb1yeIgUiOQlFaV7XwSiIkKO+a/Qtqjo1fjurO1Dz4Pf1+w/Z28A32EssCAMtZa27OhNo2z+Rd3j6Qbm6Js1rmwWWqE7JCAknm3B8W6KmVjoCTv6nvoqmar/LmNuOLbb1RNElAbn6wsPhe7GBKDserF5iMpcCUbxZAgMBAAEwDQYJKoZIhvcNAQELBQADggEBAC8c819UA+VrzIJHk0JWNWLXrTORW004IPAYItngZhVh92Be0p63NYLBnNUaVas2tp51FTGpsJtgFX0/OQyMBv6Omlud+2r6isDEmjFxulm6PHvuPWr88Dw1yMn+U2Nhxm4y1T0NFAtdZVmhMCsRP+q2mM1mQpaez1uOO0JQKAiR4tdw3UDUPNfvVyOMmV0ZYVBLlvck+h4S1bq0nMChcTPnOdk8uIuvFJlkNeNqepkBF1NmPeOWMLrcx1nJeX4jAxP00bY9AgNHqVYe4wrCBS+QVhwaqF20/FUTK6nAJRK6Y5eKY3SYqhA0IwblCf3YJ2O2msYGklDtPviz8Aak/mc="], "priority": ["-100"], "algorithm": ["RS256"]}}, {"id": "d8222e5b-f25e-4897-ba7c-4dafe1a31a2e", "name": "fallback-HS256", "providerId": "hmac-generated", "subComponents": {}, "config": {"kid": ["428aa2fe-c8cf-4d9a-95c6-254ee32c2849"], "secret": ["rkYdmTrJlRoiqKNfmYy7d5DSa-o78iPcZqCfYPJwp7ByfOaU4u5bUizobXuUNirtpDcok9HpcPbDgV8b-wGHgQ"], "priority": ["-100"], "algorithm": ["HS256"]}}]}, "internationalizationEnabled": false, "supportedLocales": [], "authenticationFlows": [{"id": "9e37b42f-a168-4dde-8429-df92162e8eee", "alias": "Account verification options", "description": "Method with which to verity the existing account", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-email-verification", "requirement": "ALTERNATIVE", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "ALTERNATIVE", "priority": 20, "flowAlias": "Verify Existing Account by Re-authentication", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "bd8fa0cd-f1b3-4337-8775-87ee6f5db990", "alias": "Authentication Options", "description": "Authentication options.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "basic-auth", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "basic-auth-otp", "requirement": "DISABLED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "auth-spnego", "requirement": "DISABLED", "priority": 30, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "de014015-37ee-4747-8d03-2dd3da6dfcca", "alias": "Browser - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "auth-otp-form", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "1f22135c-2fdc-4e04-8c7b-a6d62bf36ab0", "alias": "Direct Grant - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "direct-grant-validate-otp", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "cd2fb978-72e0-47a9-aee7-7ac63343ebc2", "alias": "First broker login - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "auth-otp-form", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "f92105b2-fe09-4707-b6d4-471be45115ac", "alias": "<PERSON><PERSON> Existing Account", "description": "Handle what to do if there is existing account with same email/username like authenticated identity provider", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-confirm-link", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "REQUIRED", "priority": 20, "flowAlias": "Account verification options", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "e68b9b4c-1427-463d-a6f5-b2d2f4432766", "alias": "Reset - Conditional OTP", "description": "Flow to determine if the OTP should be reset or not. Set to REQUIRED to force.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "reset-otp", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "5983ff8f-1e81-4412-bd2f-b64fd74e8d84", "alias": "User creation or linking", "description": "Flow for the existing/non-existing user alternatives", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticatorConfig": "create unique user config", "authenticator": "idp-create-user-if-unique", "requirement": "ALTERNATIVE", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "ALTERNATIVE", "priority": 20, "flowAlias": "<PERSON><PERSON> Existing Account", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "0dea08bd-445a-40e5-bed2-41bc15f8b5ce", "alias": "Verify Existing Account by Re-authentication", "description": "Reauthentication of existing account", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-username-password-form", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "CONDITIONAL", "priority": 20, "flowAlias": "First broker login - Conditional OTP", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "fa3f060a-efec-4b78-b8af-85a0b5b46dec", "alias": "browser", "description": "browser based authentication", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-cookie", "requirement": "ALTERNATIVE", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "auth-spnego", "requirement": "DISABLED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "identity-provider-redirector", "requirement": "ALTERNATIVE", "priority": 25, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "ALTERNATIVE", "priority": 30, "flowAlias": "forms", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "8acce794-fdeb-4a06-a99a-a18a32b3b962", "alias": "clients", "description": "Base authentication for clients", "providerId": "client-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "client-secret", "requirement": "ALTERNATIVE", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "client-jwt", "requirement": "ALTERNATIVE", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "client-secret-jwt", "requirement": "ALTERNATIVE", "priority": 30, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "client-x509", "requirement": "ALTERNATIVE", "priority": 40, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "376e6e03-cc06-459f-9e84-f774ae12a07d", "alias": "direct grant", "description": "OpenID Connect Resource Owner Grant", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "direct-grant-validate-username", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "direct-grant-validate-password", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "CONDITIONAL", "priority": 30, "flowAlias": "Direct Grant - Conditional OTP", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "18c168ec-88fe-4059-8fbb-a6139d37395b", "alias": "docker auth", "description": "Used by Docker clients to authenticate against the IDP", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "docker-http-basic-authenticator", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "b8745f1a-c7cf-45aa-9241-523af4814154", "alias": "first broker login", "description": "Actions taken after first broker login with identity provider account, which is not yet linked to any Keycloak account", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticatorConfig": "review profile config", "authenticator": "idp-review-profile", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "REQUIRED", "priority": 20, "flowAlias": "User creation or linking", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "95d348b9-0db6-4ecc-af92-1dd35c65c260", "alias": "forms", "description": "Username, password, otp and other auth forms.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-username-password-form", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "CONDITIONAL", "priority": 20, "flowAlias": "Browser - Conditional OTP", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "7e0eee55-76ff-4efd-83c2-1e9e55ac3d70", "alias": "http challenge", "description": "An authentication flow based on challenge-response HTTP Authentication Schemes", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "no-cookie-redirect", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "REQUIRED", "priority": 20, "flowAlias": "Authentication Options", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "237f32c0-cccc-4e53-90a5-bb7f0f2f0693", "alias": "registration", "description": "registration flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-page-form", "requirement": "REQUIRED", "priority": 10, "flowAlias": "registration form", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "20de52d0-dd55-4781-afba-4b5a3899fc59", "alias": "registration form", "description": "registration form", "providerId": "form-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-user-creation", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "registration-profile-action", "requirement": "REQUIRED", "priority": 40, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "registration-password-action", "requirement": "REQUIRED", "priority": 50, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "registration-recaptcha-action", "requirement": "DISABLED", "priority": 60, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "c8976fc8-4d13-4d12-a4ab-d2cfe2b03e39", "alias": "reset credentials", "description": "Reset credentials for a user if they forgot their password or something", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "reset-credentials-choose-user", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "reset-credential-email", "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "reset-password", "requirement": "REQUIRED", "priority": 30, "userSetupAllowed": false, "autheticatorFlow": false}, {"requirement": "CONDITIONAL", "priority": 40, "flowAlias": "Reset - Conditional OTP", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "7f9f568c-a9df-4967-80b7-a541377a01b0", "alias": "saml ecp", "description": "SAML ECP Profile Authentication Flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "http-basic-authenticator", "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}]}], "authenticatorConfig": [{"id": "1573f710-98f9-4afd-8b7a-5e28a875f67f", "alias": "create unique user config", "config": {"require.password.update.after.registration": "false"}}, {"id": "16ba99d6-df1c-4cfd-9967-9b9ac7778899", "alias": "review profile config", "config": {"update.profile.on.first.login": "missing"}}], "requiredActions": [{"alias": "CONFIGURE_TOTP", "name": "Configure OTP", "providerId": "CONFIGURE_TOTP", "enabled": true, "defaultAction": false, "priority": 10, "config": {}}, {"alias": "terms_and_conditions", "name": "Terms and Conditions", "providerId": "terms_and_conditions", "enabled": false, "defaultAction": false, "priority": 20, "config": {}}, {"alias": "UPDATE_PASSWORD", "name": "Update Password", "providerId": "UPDATE_PASSWORD", "enabled": true, "defaultAction": false, "priority": 30, "config": {}}, {"alias": "UPDATE_PROFILE", "name": "Update Profile", "providerId": "UPDATE_PROFILE", "enabled": true, "defaultAction": false, "priority": 40, "config": {}}, {"alias": "VERIFY_EMAIL", "name": "<PERSON><PERSON><PERSON>", "providerId": "VERIFY_EMAIL", "enabled": true, "defaultAction": false, "priority": 50, "config": {}}, {"alias": "delete_account", "name": "Delete Account", "providerId": "delete_account", "enabled": false, "defaultAction": false, "priority": 60, "config": {}}, {"alias": "update_user_locale", "name": "Update User Locale", "providerId": "update_user_locale", "enabled": true, "defaultAction": false, "priority": 1000, "config": {}}], "browserFlow": "browser", "registrationFlow": "registration", "directGrantFlow": "direct grant", "resetCredentialsFlow": "reset credentials", "clientAuthenticationFlow": "clients", "dockerAuthenticationFlow": "docker auth", "attributes": {"clientOfflineSessionMaxLifespan": "0", "clientSessionIdleTimeout": "0", "clientSessionMaxLifespan": "0", "clientOfflineSessionIdleTimeout": "0"}, "keycloakVersion": "12.0.4", "userManagedAccessAllowed": false}]