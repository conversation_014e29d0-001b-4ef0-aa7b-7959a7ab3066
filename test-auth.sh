#!/bin/bash

# Test authentication with the identity service
# This script tests the exact same authentication flow as the test

echo "🔐 Testing Authentication with Identity Service"
echo "=============================================="

# Test the authentication endpoint directly
echo "Testing token endpoint..."

curl -v \
  --location 'https://identity-stage.glideyoke.com/auth/realms/ui-qa-stage/protocol/openid-connect/token' \
  --header 'Content-Type: application/x-www-form-urlencoded' \
  --data-urlencode 'client_id=sp-services' \
  --data-urlencode 'grant_type=password' \
  --data-urlencode 'username=superadmin' \
  --data-urlencode 'password=UXWnVXxQrLbYBsKNxNmz' \
  --connect-timeout 30 \
  --max-time 60

echo ""
echo "If you see a successful response with access_token, the authentication is working!"
echo "If you see connection errors, there might be network issues or the service might be down."
