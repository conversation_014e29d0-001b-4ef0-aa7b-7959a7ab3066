#!/bin/bash

# End-to-End Test Runner for CreateUser API
# This script runs the integration tests for user creation

set -e

echo "🚀 Starting End-to-End Tests for CreateUser API"
echo "================================================"

# Check if <PERSON><PERSON> is available
if ! command -v mvn &> /dev/null; then
    echo "❌ Maven is not installed or not in PATH"
    exit 1
fi

# Check if Java is available
if ! command -v java &> /dev/null; then
    echo "❌ Java is not installed or not in PATH"
    exit 1
fi

echo "✅ Prerequisites check passed"

# Set test configuration
export QUARKUS_HTTP_TEST_PORT=8081
export QUARKUS_LOG_LEVEL=INFO

# Optional: Override identity service configuration for different environments
# export TEST_IDENTITY_SERVER_URL=https://identity-stage.glideyoke.com
# export TEST_IDENTITY_USERNAME=superadmin
# export TEST_IDENTITY_PASSWORD=UXWnVXxQrLbYBsKNxNmz

echo "🔧 Configuration:"
echo "   Test Port: ${QUARKUS_HTTP_TEST_PORT}"
echo "   Log Level: ${QUARKUS_LOG_LEVEL}"
echo "   Identity Server: ${TEST_IDENTITY_SERVER_URL:-https://identity-stage.glideyoke.com}"

echo ""
echo "🧪 Running End-to-End Tests..."
echo "================================"

# Run the specific integration test
mvn test \
    -Dtest="CreateUserEndToEndTest" \
    -Dquarkus.http.test-port=${QUARKUS_HTTP_TEST_PORT} \
    -Dquarkus.log.level=${QUARKUS_LOG_LEVEL} \
    -Dmaven.test.failure.ignore=false

TEST_RESULT=$?

echo ""
if [ $TEST_RESULT -eq 0 ]; then
    echo "✅ All tests passed successfully!"
    echo ""
    echo "📋 Test Summary:"
    echo "   ✓ User creation API tested"
    echo "   ✓ User entity verification completed"
    echo "   ✓ Auth server user verification completed"
    echo "   ✓ Group creation and assignment verified"
    echo "   ✓ Test data cleanup completed"
else
    echo "❌ Tests failed with exit code: $TEST_RESULT"
    echo ""
    echo "🔍 Troubleshooting tips:"
    echo "   1. Check if the auth service is running"
    echo "   2. Verify identity service connectivity"
    echo "   3. Check database connectivity"
    echo "   4. Review test logs for specific errors"
    echo ""
    echo "📝 For detailed logs, run:"
    echo "   mvn test -Dtest=\"CreateUserEndToEndTest\" -Dquarkus.log.category.\"com.tripudiotech.authservice\".level=DEBUG"
fi

echo ""
echo "🏁 Test execution completed"
exit $TEST_RESULT
