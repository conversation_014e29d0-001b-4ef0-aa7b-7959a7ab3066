/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.authservice.rest;

import com.tripudiotech.authservice.repository.jpa.TenantConfiguration.Fields;
import com.tripudiotech.authservice.request.CreateTenantConfigRequest;
import com.tripudiotech.authservice.request.SearchCriteria;
import com.tripudiotech.authservice.request.UpdateTenantConfigRequest;
import com.tripudiotech.authservice.response.TenantConfigurationResponse;
import com.tripudiotech.authservice.service.TenantConfigurationService;
import com.tripudiotech.base.configuration.exception.PermissionDeniedException;
import com.tripudiotech.base.constant.RequestConstants;
import com.tripudiotech.datalib.pagination.paging.PageResponse;
import com.tripudiotech.datalib.pagination.paging.SortingType;
import com.tripudiotech.securitylib.constant.RoleConstant;
import com.tripudiotech.securitylib.dto.UserInformation;
import com.tripudiotech.securitylib.service.provider.SecurityProviderServiceFactory;
import io.quarkus.oidc.IdToken;
import io.quarkus.panache.common.Sort;
import io.quarkus.security.Authenticated;
import io.smallrye.mutiny.Uni;
import java.util.UUID;
import jakarta.annotation.security.RolesAllowed;
import jakarta.inject.Inject;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.DefaultValue;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.PATCH;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.core.Response.Status;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.responses.APIResponseSchema;
import org.eclipse.microprofile.openapi.annotations.security.SecurityRequirement;

/**
 * <AUTHOR>
 */
@SecurityRequirement(name = "apiToken")
@Path("/tenant")
@Authenticated
public class TenantConfigurationResource extends RestResource {

    @Inject
    @IdToken
    JsonWebToken idToken;

    @Inject
    SecurityProviderServiceFactory securityProviderServiceFactory;

    @Inject
    TenantConfigurationService service;


    @POST
    @RolesAllowed({RoleConstant.SUPER_ADMIN, RoleConstant.ADMIN})
    @Path("/{tenant-Id}/config")
    @Operation(summary = "Create new tenant configuration")
    @APIResponseSchema(value = TenantConfigurationResponse.class)
    public Uni<Response> create(
        @PathParam("tenant-Id") String tenantId,
        CreateTenantConfigRequest request) {
        tenantValidator(tenantId);
        UserInformation userInformation =
            securityProviderServiceFactory.getDefaultAuthenticateService()
                .getCurrentUserInformation(this.tenantId);
        return
            service.add(tenantId, request, userInformation)
                .map(result -> Response.ok(result).build());
    }

    @PATCH
    @RolesAllowed({RoleConstant.SUPER_ADMIN, RoleConstant.ADMIN})
    @Path("/{tenant-Id}/config/{id}")
    @Operation(summary = "Edit tenant configuration")
    @APIResponseSchema(value = TenantConfigurationResponse.class)
    public Uni<Response> edit(@PathParam("id") UUID uuid,
        @PathParam("tenant-Id") String tenantId,
        UpdateTenantConfigRequest request) {
        tenantValidator(tenantId);
        UserInformation userInformation =
            securityProviderServiceFactory.getDefaultAuthenticateService()
                .getCurrentUserInformation(this.tenantId);
        return
            service.edit(tenantId, uuid, request, userInformation)
                .map(result -> Response.ok(result).build());
    }


    @GET
    @Operation(summary = "Get a Tenant configuration by id")
    @APIResponseSchema(value = TenantConfigurationResponse.class)
    @RolesAllowed({RoleConstant.SUPER_ADMIN, RoleConstant.ADMIN, RoleConstant.USER})
    @Path("/{tenant-Id}/config/{id}")
    public Uni<Response> get(
        @PathParam("tenant-Id") String tenantId,
        @PathParam("id") String uuid) {
        tenantValidator(tenantId);
        return service.getById(this.tenantId, UUID.fromString(uuid))
            .map(result -> Response.ok(result).build());
    }

    @DELETE
    @Operation(summary = "Delete Tenant configuration by id")
    @RolesAllowed({RoleConstant.SUPER_ADMIN, RoleConstant.ADMIN, RoleConstant.USER})
    @Path("/{tenant-Id}/config/{id}")
    public Uni<Response> delete(
        @PathParam("tenant-Id") String tenantId,
        @PathParam("id") UUID uuid) {
        tenantValidator(tenantId);
        return service.deleteById(this.tenantId, uuid)
            .onItem().transform(deleted -> deleted ? Status.NO_CONTENT : Status.NOT_FOUND)
            .onItem().transform(status -> Response.status(status).build());
    }

    @GET
    @RolesAllowed({RoleConstant.SUPER_ADMIN, RoleConstant.ADMIN, RoleConstant.USER})
    @Path("/{tenant-Id}/config")
    @Operation(summary = "Search Tenant configurations by tenant")
    @APIResponseSchema(value = PageResponse.class)
    public Uni<Response> getAll(
        @PathParam("tenant-Id") String tenantId,
        @QueryParam(RequestConstants.OFFSET_REQUEST_PARAM) @DefaultValue(RequestConstants.DEFAULT_OFFSET) Integer offset,
        @QueryParam(RequestConstants.LIMIT_REQUEST_PARAM) @DefaultValue(RequestConstants.DEFAULT_LIMIT) Integer limit,
        @QueryParam(RequestConstants.SORT_FIELD_REQUEST_PARAM) @DefaultValue(Fields.key) String sortField,
        @QueryParam(RequestConstants.SORT_FIELD_DIRECTION_REQUEST_PARAM) @DefaultValue(RequestConstants.DEFAULT_SORT_DIRECTION) SortingType direction
    ) {
        tenantValidator(tenantId);
        return
            service.search(tenantId, SearchCriteria.builder().tenantId(tenantId).
                    limit(limit)
                    .offset(offset)
                    .direction(
                        SortingType.DESCENDING.equals(direction) ? Sort.Direction.Descending :
                            Sort.Direction.Ascending)
                    .sortField(sortField).build())
                .map(result -> Response.ok(result).build());
    }

    private void tenantValidator(String tenantId) {
        if (!tenantId.equalsIgnoreCase(this.tenantId)) {
            throw new PermissionDeniedException(this.tenantId,
                "You're not allow to use this configuration!");
        }
    }

}
