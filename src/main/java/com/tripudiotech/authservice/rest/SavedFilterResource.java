/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.authservice.rest;

import com.tripudiotech.authservice.request.SavedFilterRequest;
import com.tripudiotech.authservice.service.SavedFilterService;
import com.tripudiotech.base.configuration.exception.BadRequestException;
import com.tripudiotech.base.constant.RequestConstants;
import com.tripudiotech.base.util.RestUtil;
import com.tripudiotech.datalib.db.query.SortType;
import com.tripudiotech.datalib.model.SavedFilter;
import com.tripudiotech.datalib.pagination.paging.PageResponse;
import com.tripudiotech.datalib.pagination.paging.SortingType;
import com.tripudiotech.securitylib.constant.RoleConstant;
import com.tripudiotech.securitylib.dto.UserInformation;
import com.tripudiotech.securitylib.service.provider.SecurityProviderServiceFactory;
import io.smallrye.mutiny.Uni;
import java.util.List;
import java.util.Map;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.parameters.Parameter;
import org.eclipse.microprofile.openapi.annotations.responses.APIResponseSchema;
import org.eclipse.microprofile.openapi.annotations.security.SecurityRequirement;

import jakarta.annotation.security.RolesAllowed;
import jakarta.inject.Inject;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validator;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.DefaultValue;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.util.Set;
import java.util.stream.Collectors;

@Path("/saved-filter")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@SecurityRequirement(name = "apiToken")
public class SavedFilterResource extends RestResource {

    @Inject
    SavedFilterService savedFilterService;

    @Inject
    SecurityProviderServiceFactory securityProviderServiceFactory;

    @Inject
    Validator validator;

    @POST
    @Operation(summary = "Create a new User Saved Filter")
    @APIResponseSchema(value = SavedFilter.class)
    @RolesAllowed({RoleConstant.SUPER_ADMIN, RoleConstant.ADMIN, RoleConstant.USER})
    public Uni<Response> create(SavedFilterRequest savedFilterRequest) {

        Set<ConstraintViolation<SavedFilterRequest>> violations = validator.validate(savedFilterRequest);
        if (!violations.isEmpty()) {
            throw new BadRequestException(
                    tenantId,
                    violations.parallelStream()
                            .map(violation -> String.format("[%s] %s", violation.getPropertyPath().toString(), violation.getMessage()))
                            .collect(Collectors.joining(",\n"))
            );
        }

        UserInformation userInformation =
                securityProviderServiceFactory.getDefaultAuthenticateService().getCurrentUserInformation(this.tenantId);
        return savedFilterService.create(this.tenantId, userInformation, savedFilterRequest)
                .map(result -> Response.status(Response.Status.CREATED).entity(result).build());
    }

    @GET
    @Operation(summary = "Get an User Saved Filter by id")
    @APIResponseSchema(value = SavedFilter.class)
    @RolesAllowed({RoleConstant.SUPER_ADMIN, RoleConstant.ADMIN, RoleConstant.USER})
    @Path("/{id}")
    public Uni<Response> get(@PathParam("id") String savedFilterId) {
        return savedFilterService.getById(this.tenantId, savedFilterId)
                .map(result -> Response.ok(result).build());
    }

    @GET
    @Operation(summary = "Get all Saved Filter under the logged in user")
    @APIResponseSchema(value = PageResponse.class)
    @RolesAllowed({RoleConstant.SUPER_ADMIN, RoleConstant.ADMIN, RoleConstant.USER})
    @Parameter(
        name="sort",
        description = "Sort by a list of column names and sort type separated by comma. <br /> <b>Example</b>: ['columnX,ASCENDING', 'columnY', 'columnZ,DESCENDING']"
    )
    public Uni<Response> search(
            @QueryParam(RequestConstants.OFFSET_REQUEST_PARAM) @DefaultValue(RequestConstants.DEFAULT_OFFSET) Integer offset,
            @QueryParam(RequestConstants.LIMIT_REQUEST_PARAM) @DefaultValue(RequestConstants.DEFAULT_LIMIT) Integer limit,
            @QueryParam(RequestConstants.QUERY_REQUEST_PARAM) String query,
            @QueryParam("sort") List<String> sort,
            @Deprecated @QueryParam(RequestConstants.SORT_FIELD_REQUEST_PARAM) @DefaultValue(SavedFilter.Fields.name) String sortField,
            @Deprecated @QueryParam(RequestConstants.SORT_FIELD_DIRECTION_REQUEST_PARAM) @DefaultValue(RequestConstants.DEFAULT_SORT_DIRECTION) SortingType direction
    ) {
        UserInformation userInformation =
                securityProviderServiceFactory.getDefaultAuthenticateService().getCurrentUserInformation(this.tenantId);
        Map<String, SortType> sortBy = RestUtil.getSortByFromSortQueryParam(tenantId, sort);
        return savedFilterService.search(
                        this.tenantId,
                        userInformation,
                        offset,
                        limit,
                        query,
                        sortBy
                )
                .map(result -> Response.ok(result).build());
    }

    @DELETE
    @Operation(summary = "Delete an User Saved Filter by id")
    @APIResponseSchema(value = SavedFilter.class)
    @RolesAllowed({RoleConstant.SUPER_ADMIN, RoleConstant.ADMIN, RoleConstant.USER})
    @Path("/{id}")
    public Uni<Response> delete(@PathParam("id") String savedFilterId) {
        UserInformation userInformation =
                securityProviderServiceFactory.getDefaultAuthenticateService().getCurrentUserInformation(this.tenantId);
        return savedFilterService.deleteById(this.tenantId, userInformation, savedFilterId)
                .map(result -> Response.noContent().build());
    }

}
