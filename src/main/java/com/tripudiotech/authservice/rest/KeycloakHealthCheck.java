/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.authservice.rest;

import com.tripudiotech.securitylib.service.provider.SecurityProviderServiceFactory;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.eclipse.microprofile.health.HealthCheck;
import org.eclipse.microprofile.health.HealthCheckResponse;
import org.eclipse.microprofile.health.HealthCheckResponseBuilder;
import org.eclipse.microprofile.health.Readiness;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;

@ApplicationScoped
@Readiness
@Slf4j
public class KeycloakHealthCheck implements HealthCheck {
    @Inject
    SecurityProviderServiceFactory securityProviderServiceFactory;
    @Inject
    @ConfigProperty(name = "application.defaultTenantId")
    String realmName;

    @Override
    public HealthCheckResponse call() {
        HealthCheckResponseBuilder builder = HealthCheckResponse.named("Keycloak health check");
        checkKeycloakRealm(builder);
        return builder.build();
    }

    private void checkKeycloakRealm(HealthCheckResponseBuilder builder) {
        try {
            securityProviderServiceFactory.getDefaultAuthenticateService().getWellKnownConfiguration(realmName);
            builder.up();
        } catch (Exception e) {
            e.printStackTrace();
            builder.down();
        }
    }
}
