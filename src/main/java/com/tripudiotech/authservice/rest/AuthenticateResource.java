/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.authservice.rest;

import com.tripudiotech.authservice.utils.GenericHelper;
import com.tripudiotech.securitylib.dto.LoginRequestDTO;
import com.tripudiotech.securitylib.dto.LoginResponseDTO;
import com.tripudiotech.securitylib.exception.InvalidLoginCredentialException;
import com.tripudiotech.securitylib.service.provider.SecurityProviderServiceFactory;
import io.smallrye.mutiny.Uni;
import jakarta.inject.Inject;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.HeaderParam;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import org.eclipse.microprofile.openapi.annotations.enums.SecuritySchemeType;
import org.eclipse.microprofile.openapi.annotations.security.SecurityRequirement;
import org.eclipse.microprofile.openapi.annotations.security.SecurityScheme;

import java.util.Optional;

import static com.tripudiotech.securitylib.constant.SecurityConstant.AUTHORIZATION_HEADER;
import static com.tripudiotech.securitylib.constant.SecurityConstant.TENANT_ID_HEADER;

@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@SecurityScheme(securitySchemeName = "basicAuth", type = SecuritySchemeType.HTTP, scheme = "basic")
@Path("/authenticate")
public class AuthenticateResource extends RestResource {

    SecurityProviderServiceFactory securityProviderServiceFactory;

    @Inject
    public AuthenticateResource(SecurityProviderServiceFactory securityProviderServiceFactory) {
        this.securityProviderServiceFactory = securityProviderServiceFactory;
    }

    @POST
    @SecurityRequirement(name = "basicAuth")
    public Uni<LoginResponseDTO> authenticate(@HeaderParam(TENANT_ID_HEADER) String tenantId,
                                             @HeaderParam(AUTHORIZATION_HEADER) String basicAuth) {
        return securityProviderServiceFactory.getDefaultAuthenticateService()
                .authenticate(Optional.ofNullable(GenericHelper.decodeAuthorizationBase64(basicAuth))
                        .map(LoginRequestDTO::toBuilder)
                        .map(requestBuilder -> requestBuilder.tenantId(tenantId))
                        .map(LoginRequestDTO.LoginRequestDTOBuilder::build)
                        .orElseThrow(InvalidLoginCredentialException::new));
    }
}
