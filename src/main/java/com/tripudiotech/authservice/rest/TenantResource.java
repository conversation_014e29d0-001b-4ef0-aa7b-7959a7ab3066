/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.authservice.rest;

import com.tripudiotech.authservice.service.TenantService;
import com.tripudiotech.securitylib.constant.RoleConstant;
import com.tripudiotech.securitylib.dto.request.CreateRealmRequest;
import io.quarkus.security.Authenticated;
import io.smallrye.mutiny.Uni;
import jakarta.ws.rs.GET;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.security.SecurityRequirement;

import jakarta.annotation.security.RolesAllowed;
import jakarta.inject.Inject;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.core.Response.Status;

@SecurityRequirement(name = "apiToken")
@Path("/tenant")
@Authenticated
@Slf4j
public class TenantResource {

    @Inject
    private TenantService tenantService;

    @POST
    @Operation(
            summary = "Create new tenant, only allow request with ADMIN role & from [master] tenant")
    @RolesAllowed({RoleConstant.ADMIN})
    @Path("/{tenant}")
    public Response createTenant(
            @PathParam("tenant") String tenant, CreateRealmRequest createTenantRequest) {
        return Response.status(Response.Status.CREATED)
                .entity(tenantService.createTenant(tenant, createTenantRequest))
                .build();
    }

    @GET
    @Operation(
            summary = "Get available tenants, only allow request with ADMIN role & from [master] tenant")
    @RolesAllowed({RoleConstant.ADMIN})
    @Path("/{tenant}/realms")
    public Response getAvailableTenants(@PathParam("tenant") String tenant) {
        return Response.status(Response.Status.OK).entity(tenantService.getTenants(tenant)).build();
    }

    @DELETE
    @Operation(
            summary =
                    "delete tenant, only allow request with ADMIN role & from [master] tenant and tenant must have no data node")
    @RolesAllowed({RoleConstant.ADMIN})
    @Path("/{tenant}")
    public Uni<Response> deleteTenant(@PathParam("tenant") String tenant) {
        return tenantService.deleteTenant(tenant).map(vr -> Response.status(Status.NO_CONTENT).build());
    }
}
