/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.authservice.rest;


import com.tripudiotech.base.client.dto.response.rule.RuleResult;
import com.tripudiotech.base.configuration.exception.BadRequestException;
import com.tripudiotech.base.configuration.exception.BusinessErrorCode;
import com.tripudiotech.base.configuration.exception.InvalidQueryException;
import com.tripudiotech.base.configuration.exception.ServiceException;
import com.tripudiotech.base.constant.MetricConstants;
import com.tripudiotech.base.service.ApplicationMetricService;
import com.tripudiotech.securitylib.dto.response.ErrorResponseDTO;
import io.micrometer.core.instrument.Tags;
import io.smallrye.mutiny.Uni;
import lombok.extern.slf4j.Slf4j;
import org.jboss.resteasy.reactive.RestResponse;
import org.jboss.resteasy.reactive.server.ServerExceptionMapper;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.ws.rs.ForbiddenException;
import jakarta.ws.rs.WebApplicationException;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.Request;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.core.UriInfo;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * @author: long.nguyen
 **/
@Slf4j
@ApplicationScoped
public class ExceptionGlobalHandler {

    private static final String LOG_PREFIX = "[ExceptionGlobalHandler]";

    Map<Class, RestResponse.Status> EXCEPTION_MAP = Map.ofEntries(
            Map.entry(ForbiddenException.class, RestResponse.Status.FORBIDDEN),
            Map.entry(BadRequestException.class, RestResponse.Status.BAD_REQUEST),
            Map.entry(InvalidQueryException.class, RestResponse.Status.BAD_REQUEST),
            Map.entry(IllegalArgumentException.class, RestResponse.Status.BAD_REQUEST),
            Map.entry(RuntimeException.class, RestResponse.Status.INTERNAL_SERVER_ERROR)
    );

    @Inject
    ApplicationMetricService metricService;

    @Context
    UriInfo uriInfo;

    @Context
    Request request;

    @ServerExceptionMapper(value = Exception.class)
    public Uni<Response> mapException(Exception exception) {
        log.error("{} Exception", LOG_PREFIX, exception);
        try {
            log.error("{} Exception: Uri: [{}] {}", LOG_PREFIX, request.getMethod(), uriInfo.getPath());
            RestResponse.Status responseStatus;
            String tenantId = "";
            String msg = exception.getMessage();
            if (exception instanceof ServiceException) {
                responseStatus = RestResponse.Status.fromStatusCode(((ServiceException) exception).getErrorCode());
                tenantId = ((ServiceException) exception).getTenantId();
                msg = ((ServiceException) exception).getErrorMsg();
            } else if (exception instanceof WebApplicationException) {
                responseStatus = RestResponse.Status.fromStatusCode(((WebApplicationException) exception).getResponse().getStatus());
                msg = responseStatus.getReasonPhrase();
            }else {
                log.info("{} exception is not instance of ServiceException. ExceptionType: {} {}", LOG_PREFIX,
                        exception.getClass().getSimpleName(),exception.getMessage());
                responseStatus = Optional.ofNullable(EXCEPTION_MAP.get(exception.getClass()))
                        .orElse(RestResponse.Status.INTERNAL_SERVER_ERROR);
            }

            Tags tags = Tags.of(
                    "method", request.getMethod(),
                    "path", uriInfo.getPath(),
                    "tenant_id", tenantId,
                    "exception_type", exception.getClass().getSimpleName(),
                    "exception_message", msg,
                    "response_status", responseStatus.getStatusCode() + ""
            );
            metricService.addCounterMetric(MetricConstants.API_EXCEPTION_METRIC, tags);

            Map<String,Object> metadata = new HashMap<>();
            if (exception instanceof BadRequestException) {
                List<RuleResult> ruleResults = ((BadRequestException) exception).getValidationResults();
                if (ruleResults != null) {
                    metadata.put("rule", ruleResults);
                }
            }
            return Uni.createFrom().item(Response.status(responseStatus).entity(ErrorResponseDTO.builder()
                    .statusCode(responseStatus.getStatusCode())
                    .errorMessage(msg)
                    .metadata(metadata)
                    .build()).build());
        } catch (Exception ex) {
            return Uni.createFrom().item(Response.status(BusinessErrorCode.SERVICE_INTERNAL_ERROR.getErrorCode())
                    .entity(ErrorResponseDTO.builder()
                            .statusCode(BusinessErrorCode.SERVICE_INTERNAL_ERROR.getErrorCode())
                            .errorMessage(BusinessErrorCode.SERVICE_INTERNAL_ERROR.getMessage())
                            .build()).build());
        }
    }

}
