/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.authservice.rest;

import com.tripudiotech.authservice.constant.SearchingParameterConstants;
import com.tripudiotech.authservice.request.AccountUpdateRequest;
import com.tripudiotech.authservice.service.UserService;
import com.tripudiotech.base.constant.RequestConstants;
import com.tripudiotech.datalib.model.dto.EntityWithPermission;
import com.tripudiotech.datalib.pagination.paging.PageResponse;
import com.tripudiotech.securitylib.constant.RoleConstant;
import io.quarkus.security.Authenticated;
import io.smallrye.mutiny.Uni;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.parameters.Parameter;
import org.eclipse.microprofile.openapi.annotations.responses.APIResponseSchema;
import org.eclipse.microprofile.openapi.annotations.security.SecurityRequirement;
import jakarta.annotation.security.RolesAllowed;
import jakarta.inject.Inject;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.PUT;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import jakarta.ws.rs.core.UriInfo;

@Path("/admin/user")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@Authenticated
@SecurityRequirement(name = "apiToken")
public class AdminUserResource extends RestResource {

    @Inject
    UserService userService;

    @GET
    @RolesAllowed({RoleConstant.SUPER_ADMIN, RoleConstant.ADMIN})
    @APIResponseSchema(value = PageResponse.class)
    @Operation(summary = "Search users with condition")
    public Uni<Response> searchUsers(@QueryParam(RequestConstants.OFFSET_REQUEST_PARAM) @Parameter(description = "The page number") Integer offset,
                                     @QueryParam(RequestConstants.LIMIT_REQUEST_PARAM) @Parameter(description = "Maximum results size (defaults to 100)") Integer limit,
                                     @QueryParam(SearchingParameterConstants.FIRST_NAME) @Parameter(description = "find by first name") String firstName,
                                     @QueryParam(SearchingParameterConstants.LAST_NAME) @Parameter(description = "find by last name") String lastName,
                                     @QueryParam(SearchingParameterConstants.USERNAME) @Parameter(description = "find by username") String username,
                                     @QueryParam(SearchingParameterConstants.EMAIL) @Parameter(description = "find by username") String email,
                                     @QueryParam(SearchingParameterConstants.SEARCH) @Parameter(description = "A String contained in username, first or last name, or email") String search,
                                     @Context UriInfo info) {
        return userService.searchUser(tenantId, info.getQueryParameters())
                .map(result -> Response.ok(result).build());
    }


    @PUT
    @Operation(summary = "Update user information")
    @APIResponseSchema(value = EntityWithPermission.class)
    @RolesAllowed({RoleConstant.SUPER_ADMIN, RoleConstant.ADMIN})
    @Path("/{id}")
    public Uni<Response> update(@PathParam("id") String userId,
                                AccountUpdateRequest request) {
        return userService.update(tenantId, userId, request)
                .map(result -> Response.ok(result).build());
    }

    @POST
    @Operation(summary = "Deactivate user")
    @RolesAllowed({RoleConstant.SUPER_ADMIN, RoleConstant.ADMIN})
    @Path("/{id}/deactivate")
    public Uni<Response> disableUser(@PathParam("id") String authServerUserId) {
        return userService.changeUserStatus(tenantId, authServerUserId, false)
                .map(v -> Response.status(Response.Status.OK).build());
    }

    @POST
    @Operation(summary = "Activate user")
    @RolesAllowed({RoleConstant.SUPER_ADMIN, RoleConstant.ADMIN})
    @Path("/{id}/activate")
    public Uni<Response> enableUser(@PathParam("id") String authServerUserId) {
        return userService.changeUserStatus(tenantId, authServerUserId, true)
                .map(v -> Response.status(Response.Status.OK).build());
    }

    @GET
    @Operation(summary = "Get user details")
    @RolesAllowed({RoleConstant.SUPER_ADMIN, RoleConstant.ADMIN})
    @Path("/{id}")
    public Uni<Response> getUserDetail(@PathParam("id") String authServerUserId) {
        return userService.getUserDetail(tenantId, authServerUserId)
                .map(result -> Response.ok(result).build());
    }
}