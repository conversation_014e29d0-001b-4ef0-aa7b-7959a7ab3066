/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.authservice.rest;

import com.tripudiotech.authservice.request.TeamUpdateRequest;
import com.tripudiotech.authservice.service.TeamService;
import com.tripudiotech.base.constant.RequestConstants;
import com.tripudiotech.datalib.model.dto.EntityWithPermission;
import com.tripudiotech.datalib.pagination.paging.PageResponse;
import com.tripudiotech.securitylib.constant.RoleConstant;
import io.quarkus.security.Authenticated;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.parameters.Parameter;
import org.eclipse.microprofile.openapi.annotations.responses.APIResponseSchema;
import org.eclipse.microprofile.openapi.annotations.security.SecurityRequirement;

import jakarta.annotation.security.RolesAllowed;
import jakarta.inject.Inject;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;

@Path("/team")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@Authenticated
@SecurityRequirement(name = "apiToken")
public class TeamResource extends RestResource {

    @Inject
    TeamService teamService;

    @GET
    @RolesAllowed({RoleConstant.SUPER_ADMIN, RoleConstant.ADMIN})
    @APIResponseSchema(value = PageResponse.class)
    @Operation(summary = "Search teams by team name")
    public Response searchTeams(@QueryParam(RequestConstants.OFFSET_REQUEST_PARAM) @DefaultValue(RequestConstants.DEFAULT_OFFSET) @Parameter(description = "The page number") Integer offset,
                                @QueryParam(RequestConstants.LIMIT_REQUEST_PARAM) @DefaultValue(RequestConstants.DEFAULT_LIMIT) @Parameter(description = "Maximum results size (defaults to 100)") Integer limit,
                                @QueryParam(RequestConstants.QUERY_REQUEST_PARAM) String query) {
        return Response.ok(teamService.searchTeams(tenantId, query, offset, limit)).build();
    }


    @PUT
    @Operation(summary = "Update team information")
    @APIResponseSchema(value = EntityWithPermission.class)
    @RolesAllowed({RoleConstant.SUPER_ADMIN, RoleConstant.ADMIN})
    @Path("/{id}")
    public Response update(@PathParam("id") String teamId,
                           TeamUpdateRequest request) {
        return Response.ok(teamService.update(tenantId, teamId, request))
                .build();
    }

    @DELETE
    @Operation(summary = "Delete a team")
    @APIResponseSchema(value = EntityWithPermission.class)
    @RolesAllowed({RoleConstant.SUPER_ADMIN, RoleConstant.ADMIN})
    @Path("/{id}")
    public Response removeTeam(@PathParam("id") String teamId) {
        teamService.removeTeam(tenantId, teamId);
        return Response.noContent()
                .build();
    }

    @POST
    @Operation(summary = "Add a user into a Team")
    @RolesAllowed({RoleConstant.SUPER_ADMIN, RoleConstant.ADMIN})
    @Path("/{id}/user/{userEmail}")
    public Response addUserToTeam(@PathParam("id") String teamId,
                                @PathParam("userEmail") String email) {
        teamService.addUserToGroup(tenantId, teamId, email);
        return Response.status(Response.Status.OK)
                .build();
    }

    @DELETE
    @Operation(summary = "Remove an User out of the Team")
    @RolesAllowed({RoleConstant.SUPER_ADMIN, RoleConstant.ADMIN})
    @Path("/{id}/user/{userEmail}")
    public Response removeUserToTeam(@PathParam("id") String teamId,
                                  @PathParam("userEmail") String email) {
        teamService.removeUserToGroup(tenantId, teamId, email);
        return Response.status(Response.Status.OK)
                .build();
    }

}
