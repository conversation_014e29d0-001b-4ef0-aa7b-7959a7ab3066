/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.authservice.service;

import com.tripudiotech.authservice.request.CreateTenantConfigRequest;
import com.tripudiotech.authservice.request.SearchCriteria;
import com.tripudiotech.authservice.request.UpdateTenantConfigRequest;
import com.tripudiotech.authservice.response.TenantConfigurationResponse;
import com.tripudiotech.datalib.pagination.paging.PageResponse;
import com.tripudiotech.securitylib.dto.UserInformation;
import io.smallrye.mutiny.Uni;
import java.util.UUID;

/**
 * <AUTHOR>
 */
public interface TenantConfigurationService {

    Uni<TenantConfigurationResponse> add(String tenantId, CreateTenantConfigRequest request, UserInformation userInformation);

    Uni<TenantConfigurationResponse> getById(String tenantId, UUID uuid);

    Uni<TenantConfigurationResponse> edit(String tenantId, UUID uuid, UpdateTenantConfigRequest request,
        UserInformation userInformation);

    Uni<Boolean> deleteById(String tenantId, UUID uuid);

    Uni<PageResponse<TenantConfigurationResponse>> search(String tenantId, SearchCriteria criteria);

}
