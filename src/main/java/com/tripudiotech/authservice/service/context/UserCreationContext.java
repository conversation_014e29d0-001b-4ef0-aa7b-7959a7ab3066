/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.authservice.service.context;

import com.tripudiotech.base.client.dto.request.AccountRequest;
import com.tripudiotech.datalib.model.dto.EntityWithPermission;
import com.tripudiotech.securitylib.dto.UserInformation;
import lombok.AccessLevel;
import lombok.Builder;
import lombok.Data;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;

/**
 * Context object that carries all necessary information throughout the user creation workflow.
 * This immutable context helps maintain state and provides a clean way to pass data
 * between different stages of user creation without method parameter explosion.
 * 
 * Following the Context Object pattern to improve maintainability and reduce coupling.
 */
@Data
@Builder(toBuilder = true)
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class UserCreationContext {
    
    // Core identifiers
    @NonNull String tenantId;
    @NonNull String companyId;
    @NonNull String resolvedEmail;
    @NonNull String token;
    
    // Request and user information
    @NonNull AccountRequest request;
    @NonNull UserInformation userInformation;
    
    // Entities created during the workflow
    EntityWithPermission companyEntity;
    EntityWithPermission createdEntity;
    String authServerId;
    
    /**
     * Creates a new context with the company entity set.
     * Used after company validation step.
     */
    public UserCreationContext withCompanyEntity(EntityWithPermission companyEntity) {
        return this.toBuilder()
                .companyEntity(companyEntity)
                .build();
    }
    
    /**
     * Creates a new context with the created user entity set.
     * Used after user entity creation step.
     */
    public UserCreationContext withCreatedEntity(EntityWithPermission createdEntity) {
        return this.toBuilder()
                .createdEntity(createdEntity)
                .build();
    }
    
    /**
     * Creates a new context with the auth server ID set.
     * Used after Keycloak user creation step.
     */
    public UserCreationContext withAuthServerId(String authServerId) {
        return this.toBuilder()
                .authServerId(authServerId)
                .build();
    }
    
    /**
     * Checks if the context has all required entities for permission assignment.
     */
    public boolean isReadyForPermissionAssignment() {
        return createdEntity != null && authServerId != null;
    }
    
    /**
     * Checks if the context is complete with all entities created.
     */
    public boolean isComplete() {
        return companyEntity != null && 
               createdEntity != null && 
               authServerId != null;
    }
}
