/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.authservice.service;

import com.tripudiotech.authservice.repository.jpa.Specification;
import com.tripudiotech.authservice.repository.jpa.TenantConfiguration;
import com.tripudiotech.authservice.repository.jpa.TenantConfiguration.Fields;
import com.tripudiotech.authservice.repository.jpa.TenantConfigurationRepository;
import com.tripudiotech.authservice.request.CreateTenantConfigRequest;
import com.tripudiotech.authservice.request.SearchCriteria;
import com.tripudiotech.authservice.request.UpdateTenantConfigRequest;
import com.tripudiotech.authservice.response.TenantConfigurationResponse;
import com.tripudiotech.base.configuration.exception.BadRequestException;
import com.tripudiotech.base.configuration.exception.RecordNotFoundException;
import com.tripudiotech.datalib.pagination.paging.PageInfo;
import com.tripudiotech.datalib.pagination.paging.PageResponse;
import com.tripudiotech.securitylib.dto.UserInformation;
import io.quarkus.hibernate.reactive.panache.common.WithTransaction;
import io.quarkus.panache.common.Parameters;
import io.quarkus.panache.common.Sort;
import io.smallrye.mutiny.Uni;
import io.smallrye.mutiny.unchecked.Unchecked;
import java.time.Instant;
import java.util.*;
import java.util.function.Function;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import lombok.AccessLevel;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 */
@ApplicationScoped
@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE)
public class TenantConfigurationServiceImpl implements TenantConfigurationService {

    @Inject
    TenantConfigurationRepository repository;

    @Override
    @WithTransaction
    public Uni<TenantConfigurationResponse> add(String tenantId,
        CreateTenantConfigRequest request,
        UserInformation userInformation) {
        TenantConfiguration entity = toEntity(tenantId, request, userInformation);

        Specification specification = buildSpecification(
            SearchCriteria.builder().tenantId(tenantId).key(request.getKey()).build());

        return repository
            .find(specification.getQuery(), specification.getParameters()).count().flatMap(
                Unchecked.function(val -> {
                    if (val != 0) {
                        throw new BadRequestException(tenantId,
                            String.format("Key %s is already used!", (request.getKey())));
                    }
                    return repository.persist(entity).map(getMapper(tenantId, ""));
                })
            );
    }

    @Override
    @WithTransaction
    public Uni<TenantConfigurationResponse> getById(String tenantId, UUID uuid) {
        return repository.findById(uuid.toString())
            .map(getMapper(tenantId, uuid.toString()));
    }

    @Override
    @WithTransaction
    public Uni<TenantConfigurationResponse> edit(String tenantId, UUID uuid,
        UpdateTenantConfigRequest request, UserInformation userInformation) {
        return repository.findById(uuid.toString())
            .flatMap(Unchecked.function(tenantConfiguration -> {
                    if (tenantConfiguration == null) {
                        throw new RecordNotFoundException(tenantId,
                            String.format("Can't find configuration with id %s", uuid));
                    }

                    Map<String, Object> params = new HashMap<>();
                    params.put(Fields.value, request.getValue());
                    params.put(Fields.updatedBy, userInformation.getEmail());
                    params.put(Fields.updatedDate, Instant.now());
                    params.put(Fields.id, uuid.toString());
                    params.put(Fields.tenantId, tenantId);

                    return repository.update(
                        "value = :value , updatedBy = :updatedBy, updatedDate = :updatedDate where id = :id and tenantId = :tenantId",
                        params);
                })
            )
            .flatMap(Unchecked.function(row -> {
                if (row != 1) {
                    throw new RuntimeException("Can't update");
                }
                return getById(tenantId, uuid);
            }));
    }

    @Override
    @WithTransaction
    public Uni<Boolean> deleteById(String tenantId, UUID uuid) {
        return repository.deleteById(uuid.toString());
    }

    @Override
    @WithTransaction
    public Uni<PageResponse<TenantConfigurationResponse>> search(String tenantId,
                                                                 SearchCriteria criteria) {

        log.info("Start to search {}", criteria);

        Specification specification = buildSpecification(criteria);

        Uni<Long> totalUni = repository.find(specification.getQuery(),
                specification.getParameters()).count();

        return totalUni.flatMap(
                totalRows -> {
                    if (totalRows <= 0) {
                        PageResponse<TenantConfigurationResponse> pageResponse = new PageResponse<TenantConfigurationResponse>().toBuilder()
                                .pageInfo(new PageInfo(totalRows, criteria.getLimit(), totalRows))
                                .data(new ArrayList<>())
                                .build();

                        return Uni.createFrom().item(pageResponse);
                    }
                    return repository.find(specification.getQuery(),
                                    Sort.by(criteria.getSortField()).direction(criteria.getDirection()),
                                    specification.getParameters())
                            .range(criteria.getOffset(), criteria.getOffset() + criteria.getLimit() - 1).list()
                            .map(
                                    events -> {
                                        List<TenantConfigurationResponse> responses = events.stream().map(getMapper(tenantId)).toList();

                                        log.info("Total record {} responseSize {}", totalRows,
                                                events.size());
                                        PageResponse<TenantConfigurationResponse> pageResponse = new PageResponse<TenantConfigurationResponse>().toBuilder()
                                                .pageInfo(
                                                        new PageInfo(totalRows, criteria.getLimit(), events.size()))
                                                .data(responses)
                                                .build();
                                        return pageResponse;
                                    });
                });
    }

    private Specification buildSpecification(SearchCriteria criteria) {
        StringBuilder query = new StringBuilder("tenantId = :tenantId ");
        Parameters parameters = Parameters.with(TenantConfiguration.Fields.tenantId,
            criteria.getTenantId());

        if (StringUtils.isNoneBlank(criteria.getKey())) {
            query.append(String.format(" and %s = :key", TenantConfiguration.Fields.key));
            parameters.and(TenantConfiguration.Fields.key, criteria.getKey());
        }

        if (criteria.getId() != null) {
            query.append(String.format(" and %s = :id", TenantConfiguration.Fields.id));
            parameters.and(TenantConfiguration.Fields.id, criteria.getId());
        }

        return Specification.builder().query(query.toString()).parameters(parameters).build();
    }

    private static Function<TenantConfiguration, TenantConfigurationResponse> getMapper(
        String tenantId, String uuid) {
        return tenantConfiguration -> {

            if (tenantConfiguration == null && StringUtils.isNoneBlank(uuid)) {
                throw new RecordNotFoundException(tenantId,
                    String.format("Can't find configuration with id %s", uuid));
            }
            return TenantConfigurationResponse.builder()
                .id(tenantConfiguration.getId())
                .key(tenantConfiguration.getKey())
                .value(tenantConfiguration.getValue())
                .createdDate(tenantConfiguration.getCreatedDate())
                .createdBy(tenantConfiguration.getCreatedBy())
                .updatedBy(tenantConfiguration.getUpdatedBy())
                .updatedDate(tenantConfiguration.getUpdatedDate())
                .build();
        };
    }

    private static Function<TenantConfiguration, TenantConfigurationResponse> getMapper(
        String tenantId) {
        return getMapper(tenantId, null);
    }

    private static TenantConfiguration toEntity(String tenantId,
        CreateTenantConfigRequest request,
        UserInformation userInformation) {
        TenantConfiguration entity = new TenantConfiguration();
        entity.setTenantId(tenantId);
        entity.setId(UUID.randomUUID().toString());
        entity.setKey(request.getKey());
        entity.setValue(request.getValue());
        entity.setCreatedBy(userInformation.getEmail());
        entity.setCreatedDate(Instant.now());
        return entity;
    }

}
