/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.authservice.service;

import lombok.Builder;
import lombok.Data;

/**
 * Metrics data for async thread pool monitoring
 */
@Data
@Builder
public class ThreadPoolMetrics {
    
    /**
     * Number of threads currently executing tasks
     */
    private final int activeThreads;
    
    /**
     * Current number of threads in the pool
     */
    private final int poolSize;
    
    /**
     * Core pool size (minimum number of threads)
     */
    private final int corePoolSize;
    
    /**
     * Maximum pool size
     */
    private final int maximumPoolSize;
    
    /**
     * Current number of tasks in the queue
     */
    private final int queueSize;
    
    /**
     * Maximum queue capacity
     */
    private final int queueCapacity;
    
    /**
     * Total number of tasks completed by the thread pool
     */
    private final long completedTaskCount;
    
    /**
     * Total number of tasks submitted to this service
     */
    private final long totalSubmittedTasks;
    
    /**
     * Number of successfully completed tasks tracked by this service
     */
    private final long completedTasks;
    
    /**
     * Number of failed tasks tracked by this service
     */
    private final long failedTasks;
    
    /**
     * Calculate queue utilization percentage
     */
    public double getQueueUtilization() {
        return queueCapacity > 0 ? (double) queueSize / queueCapacity * 100.0 : 0.0;
    }
    
    /**
     * Calculate thread pool utilization percentage
     */
    public double getPoolUtilization() {
        return maximumPoolSize > 0 ? (double) poolSize / maximumPoolSize * 100.0 : 0.0;
    }
    
    /**
     * Calculate active thread percentage
     */
    public double getActiveThreadPercentage() {
        return poolSize > 0 ? (double) activeThreads / poolSize * 100.0 : 0.0;
    }
    
    /**
     * Calculate success rate percentage
     */
    public double getSuccessRate() {
        long total = completedTasks + failedTasks;
        return total > 0 ? (double) completedTasks / total * 100.0 : 100.0;
    }
    
    /**
     * Check if queue is near capacity (>80%)
     */
    public boolean isQueueNearCapacity() {
        return getQueueUtilization() > 80.0;
    }
    
    /**
     * Check if thread pool is under high load (>90% active threads)
     */
    public boolean isUnderHighLoad() {
        return getActiveThreadPercentage() > 90.0;
    }
    
    /**
     * Create empty metrics for when thread pool is not available
     */
    public static ThreadPoolMetrics empty() {
        return ThreadPoolMetrics.builder()
                .activeThreads(0)
                .poolSize(0)
                .corePoolSize(0)
                .maximumPoolSize(0)
                .queueSize(0)
                .queueCapacity(0)
                .completedTaskCount(0)
                .totalSubmittedTasks(0)
                .completedTasks(0)
                .failedTasks(0)
                .build();
    }
    
    @Override
    public String toString() {
        return String.format(
            "ThreadPoolMetrics{active=%d, pool=%d/%d, queue=%d/%d (%.1f%%), " +
            "completed=%d, failed=%d, total=%d, success=%.1f%%}",
            activeThreads, poolSize, maximumPoolSize, 
            queueSize, queueCapacity, getQueueUtilization(),
            completedTasks, failedTasks, totalSubmittedTasks, getSuccessRate()
        );
    }
}
