/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.authservice.service;

import com.tripudiotech.authservice.repository.SavedFilterRepository;
import com.tripudiotech.authservice.request.SavedFilterRequest;
import com.tripudiotech.base.configuration.exception.BadRequestException;
import com.tripudiotech.base.configuration.exception.RecordNotFoundException;
import com.tripudiotech.base.service.ConverterService;
import com.tripudiotech.base.util.ReactorUtils;
import com.tripudiotech.base.util.RestUtil;
import com.tripudiotech.datalib.db.query.ConditionKeyword;
import com.tripudiotech.datalib.db.query.MatchByIdQuery;
import com.tripudiotech.datalib.db.query.RxQueryResult;
import com.tripudiotech.datalib.db.query.SortType;
import com.tripudiotech.datalib.model.SavedFilter;
import com.tripudiotech.datalib.pagination.paging.PageInfo;
import com.tripudiotech.datalib.pagination.paging.PageResponse;
import com.tripudiotech.datalib.pagination.paging.SortBy;
import com.tripudiotech.datalib.pagination.paging.SortingType;
import com.tripudiotech.securitylib.dto.UserInformation;
import io.smallrye.mutiny.Uni;
import jakarta.ws.rs.QueryParam;
import java.util.Map;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import mutiny.zero.flow.adapters.AdaptersToFlow;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONArray;
import org.json.JSONObject;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import reactor.core.publisher.Mono;

@Slf4j
@ApplicationScoped
@FieldDefaults(level = AccessLevel.PRIVATE)
public class SavedFilterService {

    @Inject
    ConverterService converterService;

    @Inject
    SavedFilterRepository savedFilterRepository;

    public Uni<SavedFilter> create(
            @NonNull String tenantId,
            @NonNull UserInformation userInformation,
            @NonNull SavedFilterRequest savedFilterRequest
    ) {
        SavedFilter savedFilter = SavedFilter.builder()
                .username(userInformation.getEmail())
                .name(savedFilterRequest.getName())
                .description(savedFilterRequest.getDescription())
                .type(savedFilterRequest.getType())
                .payload(savedFilterRequest.getPayload())
                .build();
        return Uni.createFrom().publisher(
                        AdaptersToFlow.publisher( savedFilterRepository.getOneBy(
                                tenantId,
                                savedFilterRequest.getName(),
                                userInformation.getEmail(),
                                savedFilterRequest.getType()
                        )
                ))
                .flatMap(rs -> {
                    if (StringUtils.isNoneBlank(rs.getId())) {
                        throw new BadRequestException(tenantId, "Duplicated name & type");
                    }
                    return ReactorUtils.toUni(savedFilterRepository.insert(
                            tenantId,
                            userInformation,
                            savedFilter
                    ));
                });
    }

    public Uni<SavedFilter> getById(
            @NonNull String tenantId,
            @NonNull String savedFilterId
    ) {
        return ReactorUtils.toUni( savedFilterRepository.getById(MatchByIdQuery.builder()
                        .tenantId(tenantId)
                        .entityId(savedFilterId)
                        .model(SavedFilter.class)
                        .returnVar(SavedFilter.class.getSimpleName())
                        .build())
        );
    }

    public Uni<Void> deleteById(
            @NonNull String tenantId,
            @NonNull UserInformation userInformation,
            @NonNull String savedFilterId
    ) {
        return ReactorUtils.toUni(savedFilterRepository.getByIdAndUsername(tenantId, savedFilterId, userInformation.getEmail())).map(rs -> {
                    if (StringUtils.isBlank(rs.getId())) {
                        throw new RecordNotFoundException(tenantId, "SavedFilter [" + savedFilterId + "] not found or you can not delete it.");
                    }
                    return rs;
                })
                .flatMap(rs -> savedFilterRepository.delete(tenantId, Set.of(savedFilterId))
                        .flatMap(voidResult -> Uni.createFrom().voidItem()));
    }

    public Uni<PageResponse> search(
            @NonNull String tenantId,
            @NonNull UserInformation userInformation,
            @NonNull Integer offset,
            @NonNull Integer limit,
            @Deprecated String query,
            Map<String, SortType> sortBy
    ) {
        JSONArray jsonArrays = new JSONArray();
        jsonArrays.put(new JSONObject().put(ConditionKeyword.EXACT.getValue(),
                new JSONObject().put(SavedFilter.Fields.username, userInformation.getEmail())));
        if (!StringUtils.isBlank(query)) {
            JSONObject jsonObject = new JSONObject(query);
            jsonArrays.put(jsonObject);
        }

        String jsonCriteria = new JSONObject().put(ConditionKeyword.AND.getValue(), jsonArrays).toString();
        RxQueryResult.Total<SavedFilter> queryResultReactive = savedFilterRepository.searchEntityWithPaging(
                tenantId,
                userInformation,
                jsonCriteria,
                offset,
                limit,
                sortBy
        );
        Mono<List<SavedFilter>> listResultMono = queryResultReactive.getRxData().collectList();
        Mono<List<Integer>> totalResultMono = queryResultReactive.getRxTotal().collectList();
        return ReactorUtils.toUni(Mono.zip(listResultMono, totalResultMono).map(tuple2 -> {
            List<SavedFilter> savedFilters = tuple2.getT1();
            Integer totalAttributesInDb = tuple2.getT2().stream().findFirst().orElse(0);
            return PageResponse.builder()
                    .data(new ArrayList<>(savedFilters))
                    .pageInfo(new PageInfo(totalAttributesInDb, limit, savedFilters.size()))
                    .build();
        }));
    }
}
