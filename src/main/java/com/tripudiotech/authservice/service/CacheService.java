/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 */

package com.tripudiotech.authservice.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tripudiotech.datalib.model.dto.EntityWithPermission;
import com.tripudiotech.securitylib.dto.response.RoleDto;
import io.quarkus.redis.datasource.ReactiveRedisDataSource;
import io.quarkus.redis.datasource.keys.ReactiveKeyCommands;
import io.quarkus.redis.datasource.value.ReactiveValueCommands;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.microprofile.config.inject.ConfigProperty;

import java.time.Duration;
import java.util.List;

/**
 * Reactive caching service using Redis for frequently accessed data
 */
@ApplicationScoped
@Slf4j
public class CacheService {

    @Inject
    ReactiveRedisDataSource reactiveRedisDataSource;

    @Inject
    ObjectMapper objectMapper;

    @ConfigProperty(name = "application.caching.enabled")
    boolean cachingEnabled;

    @ConfigProperty(name = "application.caching.company-validation-ttl")
    Duration companyValidationTtl;

    @ConfigProperty(name = "application.caching.role-lookup-ttl")
    Duration roleLookupTtl;

    @ConfigProperty(name = "application.caching.group-info-ttl")
    Duration groupInfoTtl;

    private ReactiveValueCommands<String, String> valueCommands;
    private ReactiveKeyCommands<String> keyCommands;

    private ReactiveValueCommands<String, String> getValueCommands() {
        if (valueCommands == null) {
            valueCommands = reactiveRedisDataSource.value(String.class);
        }
        return valueCommands;
    }

    private ReactiveKeyCommands<String> getKeyCommands() {
        if (keyCommands == null) {
            keyCommands = reactiveRedisDataSource.key();
        }
        return keyCommands;
    }

    /**
     * Cache company validation result
     */
    public Uni<Void> cacheCompanyValidation(String tenantId, String companyId, EntityWithPermission company) {
        if (!cachingEnabled) {
            return Uni.createFrom().voidItem();
        }

        String key = buildCompanyKey(tenantId, companyId);
        return Uni.createFrom().item(() -> {
            try {
                return objectMapper.writeValueAsString(company);
            } catch (Exception e) {
                log.warn("Failed to serialize company for caching: {}", e.getMessage());
                return null;
            }
        })
        .onItem().ifNotNull().transformToUni(json ->
            getValueCommands().setex(key, companyValidationTtl.getSeconds(), json)
                    .onItem().invoke(() -> log.debug("Cached company validation: {}", key))
                    .onFailure().invoke(e -> log.warn("Failed to cache company validation: {}", e.getMessage()))
                    .replaceWithVoid()
        )
        .onItem().ifNull().continueWith(() -> null);
    }

    /**
     * Get cached company validation result
     */
    public Uni<EntityWithPermission> getCachedCompanyValidation(String tenantId, String companyId) {
        if (!cachingEnabled) {
            return Uni.createFrom().nullItem();
        }

        String key = buildCompanyKey(tenantId, companyId);
        return getValueCommands().get(key)
                .onItem().ifNotNull().transform(json -> {
                    try {
                        return objectMapper.readValue(json, EntityWithPermission.class);
                    } catch (Exception e) {
                        log.warn("Failed to deserialize cached company: {}", e.getMessage());
                        return null;
                    }
                })
                .onItem().invoke(company -> {
                    if (company != null) {
                        log.debug("Cache hit for company validation: {}", key);
                    }
                })
                .onFailure().invoke(e -> log.warn("Failed to get cached company validation: {}", e.getMessage()))
                .onFailure().recoverWithNull();
    }

    /**
     * Cache role lookup results
     */
    public Uni<Void> cacheRoles(String tenantId, List<RoleDto> roles) {
        if (!cachingEnabled) {
            return Uni.createFrom().voidItem();
        }

        String key = buildRolesKey(tenantId);
        return Uni.createFrom().item(() -> {
            try {
                return objectMapper.writeValueAsString(roles);
            } catch (Exception e) {
                log.warn("Failed to serialize roles for caching: {}", e.getMessage());
                return null;
            }
        })
        .onItem().ifNotNull().transformToUni(json ->
            getValueCommands().setex(key, roleLookupTtl.getSeconds(), json)
                    .onItem().invoke(() -> log.debug("Cached roles: {}", key))
                    .onFailure().invoke(e -> log.warn("Failed to cache roles: {}", e.getMessage()))
                    .replaceWithVoid()
        )
        .onItem().ifNull().continueWith(() -> null);
    }

    /**
     * Get cached roles
     */
    public Uni<List<RoleDto>> getCachedRoles(String tenantId) {
        if (!cachingEnabled) {
            return Uni.createFrom().nullItem();
        }

        String key = buildRolesKey(tenantId);
        return getValueCommands().get(key)
                .onItem().ifNotNull().transform(json -> {
                    try {
                        TypeReference<List<RoleDto>> typeRef = new TypeReference<List<RoleDto>>() {};
                        return objectMapper.readValue(json, typeRef);
                    } catch (Exception e) {
                        log.warn("Failed to deserialize cached roles: {}", e.getMessage());
                        return null;
                    }
                })
                .onItem().invoke(roles -> {
                    if (roles != null) {
                        log.debug("Cache hit for roles: {}", key);
                    }
                })
                .onFailure().invoke(e -> log.warn("Failed to get cached roles: {}", e.getMessage()))
                .onFailure().recoverWithNull();
    }

    /**
     * Cache group information
     */
    public Uni<Void> cacheGroupInfo(String tenantId, String groupId, EntityWithPermission group) {
        if (!cachingEnabled) {
            return Uni.createFrom().voidItem();
        }

        String key = buildGroupKey(tenantId, groupId);
        return Uni.createFrom().item(() -> {
            try {
                return objectMapper.writeValueAsString(group);
            } catch (Exception e) {
                log.warn("Failed to serialize group for caching: {}", e.getMessage());
                return null;
            }
        })
        .onItem().ifNotNull().transformToUni(json ->
            getValueCommands().setex(key, groupInfoTtl.getSeconds(), json)
                    .onItem().invoke(() -> log.debug("Cached group info: {}", key))
                    .onFailure().invoke(e -> log.warn("Failed to cache group info: {}", e.getMessage()))
                    .replaceWithVoid()
        )
        .onItem().ifNull().continueWith(() -> null);
    }

    /**
     * Get cached group information
     */
    public Uni<EntityWithPermission> getCachedGroupInfo(String tenantId, String groupId) {
        if (!cachingEnabled) {
            return Uni.createFrom().nullItem();
        }

        String key = buildGroupKey(tenantId, groupId);
        return getValueCommands().get(key)
                .onItem().ifNotNull().transform(json -> {
                    try {
                        return objectMapper.readValue(json, EntityWithPermission.class);
                    } catch (Exception e) {
                        log.warn("Failed to deserialize cached group: {}", e.getMessage());
                        return null;
                    }
                })
                .onItem().invoke(group -> {
                    if (group != null) {
                        log.debug("Cache hit for group info: {}", key);
                    }
                })
                .onFailure().invoke(e -> log.warn("Failed to get cached group info: {}", e.getMessage()))
                .onFailure().recoverWithNull();
    }

    /**
     * Invalidate company cache
     */
    public Uni<Void> invalidateCompanyCache(String tenantId, String companyId) {
        if (!cachingEnabled) {
            return Uni.createFrom().voidItem();
        }

        String key = buildCompanyKey(tenantId, companyId);
        return getKeyCommands().del(key)
                .onItem().invoke(() -> log.debug("Invalidated company cache: {}", key))
                .onFailure().invoke(e -> log.warn("Failed to invalidate company cache: {}", e.getMessage()))
                .replaceWithVoid();
    }

    /**
     * Invalidate roles cache
     */
    public Uni<Void> invalidateRolesCache(String tenantId) {
        if (!cachingEnabled) {
            return Uni.createFrom().voidItem();
        }

        String key = buildRolesKey(tenantId);
        return getKeyCommands().del(key)
                .onItem().invoke(() -> log.debug("Invalidated roles cache: {}", key))
                .onFailure().invoke(e -> log.warn("Failed to invalidate roles cache: {}", e.getMessage()))
                .replaceWithVoid();
    }

    private String buildCompanyKey(String tenantId, String companyId) {
        return String.format("auth:company:%s:%s", tenantId, companyId);
    }

    private String buildRolesKey(String tenantId) {
        return String.format("auth:roles:%s", tenantId);
    }

    private String buildGroupKey(String tenantId, String groupId) {
        return String.format("auth:group:%s:%s", tenantId, groupId);
    }
}
