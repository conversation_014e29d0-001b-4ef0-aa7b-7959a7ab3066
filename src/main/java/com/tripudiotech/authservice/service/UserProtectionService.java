/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.authservice.service;

import com.tripudiotech.authservice.model.UserProtectionInfo;
import com.tripudiotech.authservice.model.UserProtectionInfo.RestrictedOperation;
import com.tripudiotech.base.configuration.exception.PermissionDeniedException;
import com.tripudiotech.securitylib.dto.UserInformation;
import jakarta.enterprise.context.ApplicationScoped;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.microprofile.config.inject.ConfigProperty;

import java.util.Set;

/**
 * Service responsible for protecting system users from unauthorized operations.
 * Follows Single Responsibility Principle by focusing only on user protection logic.
 *
 * This service centralizes all logic related to determining which users are protected
 * from certain operations like status changes, updates, or deletions.
 */
@ApplicationScoped
@Slf4j
public class UserProtectionService {

    /**
     * Set of protected usernames that cannot be modified by regular operations.
     * Configurable through application properties to support different environments.
     */
    @ConfigProperty(name = "application.security.protected-usernames", defaultValue = "admin,superadmin")
    Set<String> protectedUsernames;

    /**
     * Additional protected roles that should be treated as system accounts.
     * Configurable to allow for environment-specific protected roles.
     */
    @ConfigProperty(name = "application.security.protected-roles", defaultValue = "SUPER_ADMIN,SYSTEM_ADMIN")
    Set<String> protectedRoles;

    /**
     * Validates that a user can have their status changed (enabled/disabled).
     *
     * @param tenantId the tenant identifier for error context
     * @param user the user to validate
     * @param targetStatus the intended status (true for enable, false for disable)
     * @throws PermissionDeniedException if the user is protected or operation is invalid
     */
    public void validateUserStatusChange(@NonNull String tenantId, @NonNull UserInformation user, boolean targetStatus) {
        validateUserNotProtected(tenantId, user, "enable or disable");
        validateStatusChangeIsValid(tenantId, user, targetStatus);
    }

    /**
     * Validates that a user can be updated.
     *
     * @param tenantId the tenant identifier for error context
     * @param user the user to validate
     * @throws PermissionDeniedException if the user is protected
     */
    public void validateUserCanBeUpdated(@NonNull String tenantId, @NonNull UserInformation user) {
        validateUserNotProtected(tenantId, user, "update");
    }

    /**
     * Validates that a user can be deleted.
     *
     * @param tenantId the tenant identifier for error context
     * @param user the user to validate
     * @throws PermissionDeniedException if the user is protected
     */
    public void validateUserCanBeDeleted(@NonNull String tenantId, @NonNull UserInformation user) {
        validateUserNotProtected(tenantId, user, "delete");
    }

    /**
     * Checks if a user is protected from modifications.
     *
     * @param user the user to check
     * @return true if the user is protected, false otherwise
     */
    public boolean isUserProtected(@NonNull UserInformation user) {
        return isProtectedByUsername(user) || isProtectedByRole(user);
    }

    /**
     * Gets comprehensive protection information for a user.
     *
     * @param user the user to analyze
     * @return UserProtectionInfo containing detailed protection status
     */
    public UserProtectionInfo getUserProtectionInfo(@NonNull UserInformation user) {
        if (isProtectedByUsername(user)) {
            return UserProtectionInfo.protectedByUsername(
                user.getUsername(),
                Set.of(RestrictedOperation.STATUS_CHANGE, RestrictedOperation.UPDATE, RestrictedOperation.DELETE)
            );
        }

        if (isProtectedByRole(user)) {
            Set<String> userProtectedRoles = user.getRoles().stream()
                    .filter(role -> protectedRoles.stream()
                            .anyMatch(protectedRole -> protectedRole.equalsIgnoreCase(role)))
                    .collect(java.util.stream.Collectors.toSet());

            return UserProtectionInfo.protectedByRole(
                userProtectedRoles,
                Set.of(RestrictedOperation.STATUS_CHANGE, RestrictedOperation.UPDATE, RestrictedOperation.DELETE)
            );
        }

        return UserProtectionInfo.notProtected();
    }

    /**
     * Gets the set of protected usernames for informational purposes.
     *
     * @return immutable set of protected usernames
     */
    public Set<String> getProtectedUsernames() {
        return Set.copyOf(protectedUsernames);
    }

    /**
     * Gets the set of protected roles for informational purposes.
     *
     * @return immutable set of protected roles
     */
    public Set<String> getProtectedRoles() {
        return Set.copyOf(protectedRoles);
    }

    /**
     * Validates that a user is not protected from the specified operation.
     */
    private void validateUserNotProtected(String tenantId, UserInformation user, String operation) {
        if (isUserProtected(user)) {
            String reason = determineProtectionReason(user);
            log.warn("Attempted to {} protected user: {} ({})", operation, user.getUsername(), reason);
            throw new PermissionDeniedException(tenantId,
                String.format("You are not allowed to %s %s", operation, reason));
        }
    }

    /**
     * Validates that the status change operation is logically valid.
     */
    private void validateStatusChangeIsValid(String tenantId, UserInformation user, boolean targetStatus) {
        Boolean currentStatus = user.getEnabled();

        if (!targetStatus && (currentStatus == null || !currentStatus)) {
            throw new PermissionDeniedException(tenantId, "You are attempting to disable an inactive user");
        }

        if (targetStatus && currentStatus != null && currentStatus) {
            throw new PermissionDeniedException(tenantId, "You are attempting to enable an already active user");
        }
    }

    /**
     * Checks if user is protected by username.
     */
    private boolean isProtectedByUsername(UserInformation user) {
        if (user.getUsername() == null) {
            return false;
        }

        return protectedUsernames.stream()
                .anyMatch(protectedUsername -> protectedUsername.equalsIgnoreCase(user.getUsername()));
    }

    /**
     * Checks if user is protected by having protected roles.
     */
    private boolean isProtectedByRole(UserInformation user) {
        if (user.getRoles() == null || user.getRoles().isEmpty()) {
            return false;
        }

        return user.getRoles().stream()
                .anyMatch(userRole -> protectedRoles.stream()
                        .anyMatch(protectedRole -> protectedRole.equalsIgnoreCase(userRole)));
    }

    /**
     * Determines the reason why a user is protected for error messages.
     */
    private String determineProtectionReason(UserInformation user) {
        if (isProtectedByUsername(user)) {
            return "a system administrator account";
        }
        if (isProtectedByRole(user)) {
            return "a user with protected system roles";
        }
        return "a protected system account";
    }
}
