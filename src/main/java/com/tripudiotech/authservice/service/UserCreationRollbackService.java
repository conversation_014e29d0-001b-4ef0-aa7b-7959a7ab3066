/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.authservice.service;

import com.tripudiotech.authservice.service.context.UserCreationContext;
import com.tripudiotech.datalib.client.EntityRepository;
import com.tripudiotech.securitylib.service.SecurityProviderService;
import com.tripudiotech.securitylib.service.SecurityProviderServiceFactory;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import lombok.extern.slf4j.Slf4j;

/**
 * Service responsible for handling rollback operations during user creation failures.
 * Implements the Compensation Pattern to undo partially completed operations
 * and maintain data consistency across different systems (Database, Keycloak).
 */
@Slf4j
@ApplicationScoped
public class UserCreationRollbackService {

    @Inject
    EntityRepository entityRepository;

    @Inject
    SecurityProviderServiceFactory securityProviderServiceFactory;

    @Inject
    AsyncConfigurationService asyncConfigurationService;

    /**
     * Performs rollback of Person entity creation.
     * This is called when Keycloak user creation fails after Person entity was created.
     *
     * @param context the user creation context containing rollback information
     * @return Uni that completes when rollback is finished
     */
    public Uni<Void> rollbackPersonEntity(UserCreationContext context) {
        if (!context.isPersonEntityCreated() || context.getCreatedEntity() == null) {
            log.debug("No Person entity to rollback for user: {}", context.getResolvedEmail());
            return Uni.createFrom().voidItem();
        }

        String entityId = context.getCreatedEntity().getId();
        log.warn("Rolling back Person entity creation for user: {} (Entity ID: {})", 
                context.getResolvedEmail(), entityId);

        return entityRepository.deleteEntity(
                        context.getToken(),
                        context.getTenantId(),
                        entityId
                )
                .onItem().invoke(response -> {
                    if (response.getStatus() >= 200 && response.getStatus() < 300) {
                        log.info("Successfully rolled back Person entity for user: {} (Entity ID: {})",
                                context.getResolvedEmail(), entityId);
                    } else {
                        log.error("Failed to rollback Person entity for user: {} (Entity ID: {}). Status: {}",
                                context.getResolvedEmail(), entityId, response.getStatus());
                    }
                })
                .onFailure().invoke(throwable -> 
                        log.error("Exception during Person entity rollback for user: {} (Entity ID: {})",
                                context.getResolvedEmail(), entityId, throwable)
                )
                .replaceWithVoid()
                .runSubscriptionOn(asyncConfigurationService.getCustomExecutor());
    }

    /**
     * Performs rollback of Keycloak user creation.
     * This is called when permission assignment fails after Keycloak user was created.
     *
     * @param context the user creation context containing rollback information
     * @return Uni that completes when rollback is finished
     */
    public Uni<Void> rollbackKeycloakUser(UserCreationContext context) {
        if (!context.isKeycloakUserCreated() || context.getAuthServerId() == null) {
            log.debug("No Keycloak user to rollback for user: {}", context.getResolvedEmail());
            return Uni.createFrom().voidItem();
        }

        String authServerId = context.getAuthServerId();
        log.warn("Rolling back Keycloak user creation for user: {} (Auth ID: {})", 
                context.getResolvedEmail(), authServerId);

        return Uni.createFrom().item(() -> {
                    try {
                        SecurityProviderService securityProviderService = 
                                securityProviderServiceFactory.getDefaultAuthenticateService();
                        securityProviderService.deleteUser(context.getTenantId(), authServerId);
                        
                        log.info("Successfully rolled back Keycloak user for user: {} (Auth ID: {})",
                                context.getResolvedEmail(), authServerId);
                        return null;
                    } catch (Exception e) {
                        log.error("Failed to rollback Keycloak user for user: {} (Auth ID: {})",
                                context.getResolvedEmail(), authServerId, e);
                        throw e;
                    }
                })
                .onFailure().invoke(throwable -> 
                        log.error("Exception during Keycloak user rollback for user: {} (Auth ID: {})",
                                context.getResolvedEmail(), authServerId, throwable)
                )
                .replaceWithVoid()
                .runSubscriptionOn(asyncConfigurationService.getCustomExecutor());
    }

    /**
     * Performs rollback of permission assignments.
     * Note: This is typically not needed as permission failures should trigger
     * full rollback of both Person entity and Keycloak user.
     *
     * @param context the user creation context containing rollback information
     * @return Uni that completes when rollback is finished
     */
    public Uni<Void> rollbackPermissions(UserCreationContext context) {
        if (!context.isPermissionsAssigned()) {
            log.debug("No permissions to rollback for user: {}", context.getResolvedEmail());
            return Uni.createFrom().voidItem();
        }

        log.warn("Rolling back permission assignments for user: {} (Auth ID: {})", 
                context.getResolvedEmail(), context.getAuthServerId());

        // Note: Permission rollback is complex and typically handled by deleting the user entirely
        // Individual permission removal would require tracking which permissions were assigned
        log.info("Permission rollback completed via user deletion for user: {}", 
                context.getResolvedEmail());
        
        return Uni.createFrom().voidItem();
    }

    /**
     * Performs complete rollback of all created entities and assignments.
     * This is the main rollback method that orchestrates cleanup of all resources.
     *
     * @param context the user creation context containing rollback information
     * @return Uni that completes when all rollback operations are finished
     */
    public Uni<Void> performFullRollback(UserCreationContext context) {
        log.warn("Performing full rollback for user creation: {}", context.getResolvedEmail());

        // Rollback in reverse order of creation: Permissions -> Keycloak -> Person Entity
        return rollbackPermissions(context)
                .chain(() -> rollbackKeycloakUser(context))
                .chain(() -> rollbackPersonEntity(context))
                .onItem().invoke(() -> 
                        log.info("Full rollback completed for user: {}", context.getResolvedEmail())
                )
                .onFailure().invoke(throwable -> 
                        log.error("Full rollback failed for user: {}", context.getResolvedEmail(), throwable)
                );
    }

    /**
     * Performs partial rollback based on the current state of the context.
     * Determines what needs to be rolled back based on what was successfully created.
     *
     * @param context the user creation context containing rollback information
     * @return Uni that completes when appropriate rollback operations are finished
     */
    public Uni<Void> performContextAwareRollback(UserCreationContext context) {
        log.warn("Performing context-aware rollback for user: {}", context.getResolvedEmail());

        if (context.isPermissionsAssigned()) {
            // If permissions were assigned, rollback everything
            return performFullRollback(context);
        } else if (context.isKeycloakUserCreated()) {
            // If Keycloak user was created but permissions failed, rollback Keycloak and Person
            return rollbackKeycloakUser(context)
                    .chain(() -> rollbackPersonEntity(context));
        } else if (context.isPersonEntityCreated()) {
            // If only Person entity was created, rollback just that
            return rollbackPersonEntity(context);
        } else {
            // Nothing to rollback
            log.debug("No rollback needed for user: {}", context.getResolvedEmail());
            return Uni.createFrom().voidItem();
        }
    }
}
