/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.authservice.service;

import io.smallrye.mutiny.Uni;
import io.smallrye.mutiny.infrastructure.Infrastructure;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import jakarta.enterprise.context.ApplicationScoped;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.microprofile.config.inject.ConfigProperty;

import java.time.Duration;
import java.util.concurrent.Executor;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Service for managing async processing configuration and custom thread pool
 * for post-creation tasks like email verification and notifications.
 */
@ApplicationScoped
@Slf4j
@Getter
public class AsyncConfigurationService {

    @ConfigProperty(name = "application.async.post-creation-tasks")
    boolean postCreationTasksEnabled;

    @ConfigProperty(name = "application.async.thread-pool-size")
    int threadPoolSize;

    @ConfigProperty(name = "application.async.queue-capacity")
    int queueCapacity;

    @ConfigProperty(name = "application.async.task-timeout")
    Duration taskTimeout;

    @ConfigProperty(name = "application.async.keep-alive-time")
    Duration keepAliveTime;

    @ConfigProperty(name = "application.async.thread-name-prefix")
    String threadNamePrefix;

    @ConfigProperty(name = "application.async.metrics-enabled")
    boolean metricsEnabled;

    private ThreadPoolExecutor customThreadPool;
    private final AtomicLong completedTasks = new AtomicLong(0);
    private final AtomicLong failedTasks = new AtomicLong(0);
    private final AtomicLong totalSubmittedTasks = new AtomicLong(0);

    @PostConstruct
    void initialize() {
        validateConfiguration();
        createCustomThreadPool();
        log.info("AsyncConfigurationService initialized with thread pool size: {}, queue capacity: {}, task timeout: {}",
                threadPoolSize, queueCapacity, taskTimeout);
    }

    @PreDestroy
    void shutdown() {
        if (customThreadPool != null && !customThreadPool.isShutdown()) {
            log.info("Shutting down async thread pool...");
            customThreadPool.shutdown();
            try {
                if (!customThreadPool.awaitTermination(30, TimeUnit.SECONDS)) {
                    log.warn("Thread pool did not terminate gracefully, forcing shutdown");
                    customThreadPool.shutdownNow();
                }
                log.info("Async thread pool shutdown completed");
            } catch (InterruptedException e) {
                log.error("Interrupted while waiting for thread pool shutdown", e);
                customThreadPool.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }

    /**
     * Execute an async task using the custom thread pool
     */
    public <T> Uni<T> executeAsync(Uni<T> task) {
        if (!postCreationTasksEnabled) {
            log.debug("Async post-creation tasks are disabled, executing synchronously");
            return task;
        }

        totalSubmittedTasks.incrementAndGet();

        return task
                .runSubscriptionOn(customThreadPool)
                .onItem().invoke(result -> {
                    completedTasks.incrementAndGet();
                    if (metricsEnabled) {
                        logMetrics();
                    }
                })
                .onFailure().invoke(throwable -> {
                    failedTasks.incrementAndGet();
                    log.warn("Async task failed: {}", throwable.getMessage());
                    if (metricsEnabled) {
                        logMetrics();
                    }
                });
    }

    /**
     * Execute a void async task using the custom thread pool
     */
    public Uni<Void> executeAsyncVoid(Uni<Void> task) {
        return executeAsync(task);
    }

    /**
     * Execute a runnable task asynchronously
     */
    public Uni<Void> executeAsync(Runnable task) {
        if (!postCreationTasksEnabled) {
            log.debug("Async post-creation tasks are disabled, executing synchronously");
            return Uni.createFrom().item(() -> {
                task.run();
                return null;
            });
        }

        totalSubmittedTasks.incrementAndGet();

        return Uni.createFrom().item(() -> {
                    task.run();
                    return (Void) null;
                })
                .runSubscriptionOn(customThreadPool)
                .onItem().invoke(result -> {
                    completedTasks.incrementAndGet();
                    if (metricsEnabled) {
                        logMetrics();
                    }
                })
                .onFailure().invoke(throwable -> {
                    failedTasks.incrementAndGet();
                    log.warn("Async task failed: {}", throwable.getMessage());
                    if (metricsEnabled) {
                        logMetrics();
                    }
                });
    }

    /**
     * Get the custom thread pool executor for direct use
     */
    public Executor getCustomExecutor() {
        return customThreadPool != null ? customThreadPool : Infrastructure.getDefaultWorkerPool();
    }

    /**
     * Get current thread pool metrics
     */
    public ThreadPoolMetrics getMetrics() {
        if (customThreadPool == null) {
            return ThreadPoolMetrics.empty();
        }

        return ThreadPoolMetrics.builder()
                .activeThreads(customThreadPool.getActiveCount())
                .poolSize(customThreadPool.getPoolSize())
                .corePoolSize(customThreadPool.getCorePoolSize())
                .maximumPoolSize(customThreadPool.getMaximumPoolSize())
                .queueSize(customThreadPool.getQueue().size())
                .queueCapacity(queueCapacity)
                .completedTaskCount(customThreadPool.getCompletedTaskCount())
                .totalSubmittedTasks(totalSubmittedTasks.get())
                .completedTasks(completedTasks.get())
                .failedTasks(failedTasks.get())
                .build();
    }

    private void validateConfiguration() {
        if (threadPoolSize <= 0) {
            throw new IllegalArgumentException("Thread pool size must be positive, got: " + threadPoolSize);
        }
        if (queueCapacity <= 0) {
            throw new IllegalArgumentException("Queue capacity must be positive, got: " + queueCapacity);
        }
        if (taskTimeout.isNegative() || taskTimeout.isZero()) {
            throw new IllegalArgumentException("Task timeout must be positive, got: " + taskTimeout);
        }
        if (keepAliveTime.isNegative()) {
            throw new IllegalArgumentException("Keep alive time must be non-negative, got: " + keepAliveTime);
        }
    }

    private void createCustomThreadPool() {
        ThreadFactory threadFactory = new AsyncThreadFactory(threadNamePrefix);

        customThreadPool = new ThreadPoolExecutor(
                threadPoolSize,                           // corePoolSize
                threadPoolSize,                           // maximumPoolSize (fixed size pool)
                keepAliveTime.toSeconds(),                // keepAliveTime
                TimeUnit.SECONDS,                         // unit
                new LinkedBlockingQueue<>(queueCapacity), // workQueue
                threadFactory,                            // threadFactory
                new ThreadPoolExecutor.CallerRunsPolicy() // rejectionHandler
        );

        // Allow core threads to timeout
        customThreadPool.allowCoreThreadTimeOut(true);

        log.info("Created custom thread pool: coreSize={}, maxSize={}, queueCapacity={}, keepAlive={}s",
                threadPoolSize, threadPoolSize, queueCapacity, keepAliveTime.toSeconds());
    }

    private void logMetrics() {
        if (totalSubmittedTasks.get() % 10 == 0) { // Log every 10 tasks
            ThreadPoolMetrics metrics = getMetrics();
            log.debug("Async thread pool metrics - Active: {}, Pool: {}/{}, Queue: {}/{}, " +
                     "Completed: {}, Failed: {}, Total: {}",
                    metrics.getActiveThreads(), metrics.getPoolSize(), metrics.getMaximumPoolSize(),
                    metrics.getQueueSize(), metrics.getQueueCapacity(),
                    metrics.getCompletedTasks(), metrics.getFailedTasks(), metrics.getTotalSubmittedTasks());
        }
    }

    /**
     * Custom thread factory for async tasks
     */
    private static class AsyncThreadFactory implements ThreadFactory {
        private final AtomicInteger threadNumber = new AtomicInteger(1);
        private final String namePrefix;

        AsyncThreadFactory(String namePrefix) {
            this.namePrefix = namePrefix;
        }

        @Override
        public Thread newThread(Runnable r) {
            Thread thread = new Thread(r, namePrefix + threadNumber.getAndIncrement());
            thread.setDaemon(true);
            thread.setPriority(Thread.NORM_PRIORITY);
            return thread;
        }
    }
}
