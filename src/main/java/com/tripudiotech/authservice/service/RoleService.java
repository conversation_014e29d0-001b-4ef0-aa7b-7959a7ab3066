/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.authservice.service;

import com.tripudiotech.authservice.repository.RoleRepository;
import com.tripudiotech.authservice.request.CreateRoleRequest;
import com.tripudiotech.authservice.response.RoleResponse;
import com.tripudiotech.base.configuration.exception.BusinessErrorCode;
import com.tripudiotech.base.configuration.exception.RecordNotFoundException;
import com.tripudiotech.base.configuration.exception.ServiceException;
import com.tripudiotech.datalib.model.OrgRole;
import com.tripudiotech.securitylib.dto.UserInformation;
import com.tripudiotech.securitylib.dto.response.RoleDto;
import com.tripudiotech.securitylib.exception.DeleteRoleRestrictedException;
import com.tripudiotech.securitylib.service.provider.SecurityProviderServiceFactory;
import io.smallrye.mutiny.Uni;
import io.smallrye.mutiny.infrastructure.Infrastructure;
import io.smallrye.mutiny.unchecked.Unchecked;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.jboss.resteasy.reactive.ClientWebApplicationException;

import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.ws.rs.InternalServerErrorException;
import org.reactivestreams.FlowAdapters;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Slf4j
@ApplicationScoped
@FieldDefaults(level = AccessLevel.PRIVATE)
public class RoleService {

    static final String ORG_ROLE_LABEL = "OrgRole";

    @Inject
    SecurityProviderServiceFactory securityProviderServiceFactory;

    @Inject
    RoleRepository roleRepository;

    public Uni<List<RoleResponse>> getRoles(
            @NonNull final String tenantId,
            @NonNull UserInformation userInformation
    ) {
        return Uni.createFrom()
                .publisher(
                        FlowAdapters.toFlowPublisher(
                            roleRepository.getAllRoles(tenantId, userInformation)))
                .flatMap(data -> {
                    List<RoleResponse> roleDtos = data.stream()
                        //Fixme[Tin Nguyen] we will update the logic here more generic to hide the role without isGlideRole = true. It requires to update the OrgRole data model to have the attribute set up
                        .filter(orgRole ->  !orgRole.getName().equalsIgnoreCase("admin") && !orgRole.getName().equalsIgnoreCase("user") )
                            .map(orgRole -> RoleResponse.builder()
                                    .id(orgRole.getId())
                                    .name(orgRole.getName())
                                    .description(orgRole.getDescription())
                                    .build()
                            ).collect(Collectors.toList());
                    return Uni.createFrom().item(roleDtos);
                });
    }

    public Uni<RoleResponse> createRole(
            @NonNull String tenantId,
            @NonNull CreateRoleRequest roleDto,
            @NonNull UserInformation userInformation
    ) {
        return roleRepository
                .insert(tenantId, userInformation, OrgRole.builder().name(roleDto.getName()).description(roleDto.getDescription()).build())
                .flatMap(orgRole -> {
                    String entityId = orgRole.getId();
                    String roleName = orgRole.getName();
                    return Uni.createFrom()
                        .item(() -> createKeycloakRole(tenantId, entityId, roleName, orgRole.getDescription()))
                        .runSubscriptionOn(Infrastructure.getDefaultWorkerPool())
                        .onFailure()
                        .transform(throwable -> {
                          log.error("Call Keycloak to create ROLE error", throwable);
                          throw new RuntimeException(throwable);
                        })
                          .flatMap(roleResp -> {
                              if (roleResp == null) {
                                  return roleRepository.delete(tenantId, Set.of(orgRole.getId()))
                                          .flatMap(response -> Uni.createFrom().nullItem());
                              }
                              return Uni.createFrom().item(RoleResponse.builder()
                                      .name(roleResp.getName())
                                      .description(orgRole.getDescription())
                                      .id(entityId).build());
                          });
                });
    }

    private RoleDto createKeycloakRole(String tenantId, String entityId, String roleName, String description) {
        Map<String, List<String>> attributes = new HashMap<>();
        attributes.put("isGlideRole", Collections.singletonList("true"));
        attributes.put("orgRoleId", Collections.singletonList(entityId));
        return securityProviderServiceFactory.getDefaultAuthenticateService()
                .createRole(tenantId, roleName, description, attributes);
    }

    public Uni<Void> deleteRole(
            @NonNull String tenantId,
            @NonNull String entityId
    ) {
        return roleRepository.getById(
                tenantId,
                entityId
        ).flatMap(Unchecked.function(orgRole -> {
            if (StringUtils.isBlank(orgRole.getName()) && StringUtils.isBlank(orgRole.getId())) {
                throw new RecordNotFoundException(tenantId, "Can not find role id " + entityId);
            }
            if (orgRole.isSystem()) {
                throw new ServiceException(tenantId, BusinessErrorCode.BAD_REQUEST, "System role can not be deleted");
            }
            var roleName = orgRole.getName();
            if (StringUtils.isNotBlank(roleName)) {
              return Uni.createFrom()
                  .voidItem()
                  .invoke(() -> securityProviderServiceFactory.getDefaultAuthenticateService()
                      .deleteRole(tenantId, roleName))
                  .runSubscriptionOn(Infrastructure.getDefaultWorkerPool())
                  .onFailure()
                  .transform(throwable -> {
                    if (throwable instanceof ClientWebApplicationException) {
                      if (((ClientWebApplicationException) throwable).getResponse().getStatus()
                          == HttpStatus.SC_NOT_FOUND) {
                        log.info(
                            "[RoleService] Role is not found in keycloak. Potentially it's being deleted before. TenantId: {}, RoleName: {}",
                            tenantId, roleName);
                        throw new RuntimeException(throwable);
                      }
                    } else if (throwable instanceof DeleteRoleRestrictedException) {
                        throw new ServiceException(tenantId, BusinessErrorCode.BAD_REQUEST, throwable.getMessage());
                    }
                    return throwable;
                  }).flatMap(res -> roleRepository.delete(tenantId, Set.of(entityId))).onFailure().retry().atMost(1).onFailure().invoke(ex -> {
                            log.info("delete role {} in db failed ", roleName, ex);
                            Uni.createFrom()
                                .item(() -> createKeycloakRole(tenantId, entityId, roleName, orgRole.getDescription()))
                                .runSubscriptionOn(Infrastructure.getDefaultWorkerPool())
                                .onFailure()
                                .transform(throwable -> {
                                  log.error("Can not re-create role {} in keycloak {}", roleName, throwable.getMessage(), throwable);
                                  throw new RuntimeException(throwable);
                                }).subscribe().with(
                                    result -> log.info("Successfully re-created role {}", roleName),
                                    error -> log.error("Failed to re-create role {}", roleName, error)
                                );
                        });
            }
            throw new InternalServerErrorException("Can not delete role " + roleName);
        }));
    }
}
