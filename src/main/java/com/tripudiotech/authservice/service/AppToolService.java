/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.authservice.service;

import com.tripudiotech.authservice.repository.AppToolRepository;
import com.tripudiotech.authservice.request.AppToolRequest;
import com.tripudiotech.base.configuration.exception.BadRequestException;
import com.tripudiotech.base.configuration.exception.RecordNotFoundException;
import com.tripudiotech.base.service.ConverterService;
import com.tripudiotech.base.util.ReactorUtils;
import com.tripudiotech.base.util.RestUtil;
import com.tripudiotech.datalib.db.DBConstants;
import com.tripudiotech.datalib.db.query.ConditionKeyword;
import com.tripudiotech.datalib.db.query.InsertQuery;
import com.tripudiotech.datalib.db.query.Query;
import com.tripudiotech.datalib.db.query.RxQueryResult;
import com.tripudiotech.datalib.db.query.SortType;
import com.tripudiotech.datalib.model.AppTool;
import com.tripudiotech.datalib.model.SysRoot;
import com.tripudiotech.datalib.model.dynamic.Relation;
import com.tripudiotech.datalib.pagination.paging.PageInfo;
import com.tripudiotech.datalib.pagination.paging.PageResponse;
import com.tripudiotech.datalib.pagination.paging.SortBy;
import com.tripudiotech.datalib.pagination.paging.SortingType;
import com.tripudiotech.securitylib.dto.UserInformation;
import io.smallrye.mutiny.Uni;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import mutiny.zero.flow.adapters.AdaptersToFlow;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import reactor.core.publisher.Mono;

@Slf4j
@ApplicationScoped
@FieldDefaults(level = AccessLevel.PRIVATE)
public class AppToolService {

    @Inject
    ConverterService converterService;

    @Inject
    AppToolRepository appToolRepository;

    public Uni<AppTool> create(
            @NonNull String tenantId,
            @NonNull UserInformation userInformation,
            @NonNull AppToolRequest appToolRequest
    ) {
        AppTool appTool = AppTool.builder()
                .name(appToolRequest.getName())
                .description(appToolRequest.getDescription())
                .logo(appToolRequest.getLogo())
                .build();

        Set<String> linkAppToolNames = appToolRequest.getInterfaceWiths();
        return ReactorUtils.toUni( appToolRepository.getOneByName(
                                tenantId,
                                appToolRequest.getName(),
                                userInformation
                        )
                )
                .flatMap(rs -> {
                    if (StringUtils.isNoneBlank(rs.getId())) {
                        throw new BadRequestException(tenantId, "There is an AppTool with name [" + appTool.getName() + "] already exists");
                    }

                    Uni<Map<String, AppTool>> appToolUnis = getByNames(tenantId, linkAppToolNames, userInformation);

                    return appToolUnis.flatMap(appToolByName->{
                        for (String appToolName : linkAppToolNames) {
                            if (!appToolByName.containsKey(appToolName)) {
                                throw new BadRequestException(tenantId, "App Tool with name [" + appToolName + "] not found");
                            }
                        }
                        List<Query> queries = new ArrayList<>();
                        queries.add(
                            InsertQuery.builder()
                                .tenantId(tenantId)
                                .authoredBy(userInformation.getEmail())
                                .entity(appTool)
                                .build()
                        );
                        for (String name : linkAppToolNames) {
                            queries.add(
                                InsertQuery.builder()
                                    .entity(
                                        Relation.builder()
                                            .fromNode(appTool)
                                            .relationName(DBConstants.RELATION_INTERFACE_WITH)
                                            .toNode(SysRoot.builder().id(appToolByName.get(name).getId()).build())
                                            .build()
                                    )
                                    .authoredBy(userInformation.getEmail())
                                    .tenantId(tenantId)
                                    .build()
                            );
                        }
                        return ReactorUtils.toUni(appToolRepository.write(queries).collectList())
                            .flatMap(result -> {
                                appToolRepository.validateWriteQueryResult(tenantId, queries, result);
                                return ReactorUtils.toUni(appToolRepository.getOneByName(tenantId, appTool.getName(), userInformation));
                            });
                    });
                });
    }

    public Uni<AppTool> getByName(
            @NonNull String tenantId,
            @NonNull String name,
            @NonNull UserInformation userInformation
    ) {
        return ReactorUtils.toUni( appToolRepository.getOneByName(
                        tenantId,
                        name,
                        userInformation
                )
        );
    }

    public Uni<Map<String, AppTool>> getByNames(
        @NonNull String tenantId,
        @NonNull Set<String> names,
        @NonNull UserInformation userInformation
    ) {
        if (names.isEmpty()) {
            return Uni.createFrom().item(Collections.emptyMap());
        }
        // {"$in": {"name": ["id1", "id2"]}}
        JSONArray jsonArray = new JSONArray();
        for (String name : names) {
            jsonArray.put(name);
        }
        JSONObject nameJsonObj = new JSONObject().put(DBConstants.NAME_PROPERTY, jsonArray);
        JSONObject inCondition = new JSONObject().put(ConditionKeyword.IN.getValue(), nameJsonObj);
        return ReactorUtils.toUni(appToolRepository.getAllByCriteriaString(tenantId, inCondition.toString(), userInformation.getEmail(), Set.of(AppTool.class.getSimpleName()))
                    .collectList()
            ).map(listResult-> listResult.stream()
                .collect(Collectors.toMap(AppTool::getName, element->element)));
    }

    public Uni<Void> deleteByName(
            @NonNull String tenantId,
            @NonNull UserInformation userInformation,
            @NonNull String name
    ) {
        return getByName(tenantId, name, userInformation)
                .flatMap(appTool -> {
                    String appToolId = appTool.getId();
                    if (StringUtils.isBlank(appToolId)) {
                        throw new RecordNotFoundException(tenantId, "App tool with name [" + name + "]  not found");
                    }
                    if (appTool.isSystem()) {
                        throw new BadRequestException(tenantId, "Deleting the system App Tool is not allowed");
                    }
                    return ReactorUtils.toUni(appToolRepository.delete(tenantId, Set.of(appToolId)));
                });
    }

    public Uni<PageResponse> search(
            @NonNull String tenantId,
            @NonNull UserInformation userInformation,
            @NonNull Integer offset,
            @NonNull Integer limit,
            String query,
            Map<String, SortType> sortBy
    ) {
        RxQueryResult.Total<AppTool> queryResultReactive = appToolRepository.searchEntityWithPaging(
                tenantId,
                userInformation,
                query,
                offset,
                limit,
                sortBy
        );
        Mono<List<AppTool>> listResultMono = queryResultReactive.getRxData().collectList();
        Mono<List<Integer>> totalResultMono = queryResultReactive.getRxTotal().collectList();
        return ReactorUtils.toUni(Mono.zip(listResultMono, totalResultMono).map(tuple2 -> {
            List<AppTool> appToolList = tuple2.getT1();
            Integer totalAttributesInDb = tuple2.getT2().stream().findFirst().orElse(0);
            return PageResponse.builder()
                    .data(new ArrayList<>(appToolList))
                    .pageInfo(new PageInfo(totalAttributesInDb, limit, appToolList.size()))
                    .build();
        }));
    }
}
