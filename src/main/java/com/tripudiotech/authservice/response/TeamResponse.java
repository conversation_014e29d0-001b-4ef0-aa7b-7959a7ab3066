/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.authservice.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.tripudiotech.datalib.db.DBConstants;
import com.tripudiotech.datalib.model.dto.EntityWithPermission;
import lombok.AccessLevel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;

import java.util.Collections;
import java.util.Map;

/**
 * @author: long.nguyen
 **/
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@FieldDefaults(level = AccessLevel.PRIVATE)
public class TeamResponse extends EntityWithPermission {

    @NonNull
    String authId;

    String companyId;

    // The field to know which group is not linked between keycloak and neo4j
    boolean hasReferenceLink;

    public TeamResponse(@NonNull String authId,
                        @NonNull String companyId,
                        @NonNull EntityWithPermission entityWithPermission) {
        this.setId(entityWithPermission.getId());
        this.setAuthId(authId);
        this.setCompanyId(companyId);
        this.setCreatedAt(entityWithPermission.getCreatedAt());
        this.setUpdatedAt(entityWithPermission.getUpdatedAt());
        this.setPermissions(entityWithPermission.getPermissions());
        this.setProperties(entityWithPermission.getProperties());
        this.setState(entityWithPermission.getState());
        this.setOwner(entityWithPermission.getOwner());
        this.setCreatedBy(entityWithPermission.getCreatedBy());
        this.setDisabled(entityWithPermission.isDisabled());
        this.setHasReferenceLink(true);
    }

    public TeamResponse(@NonNull String authId,
                        @NonNull String companyId,
                        @NonNull String name) {
        this.setAuthId(authId);
        this.setCompanyId(companyId);
        this.setPermissions(Collections.emptyMap());
        this.setProperties(Map.of(DBConstants.NAME_PROPERTY, name));
        this.setHasReferenceLink(false);
    }

}
