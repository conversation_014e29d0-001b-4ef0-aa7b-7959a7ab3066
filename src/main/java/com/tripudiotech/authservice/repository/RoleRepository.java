/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.authservice.repository;

import com.tripudiotech.base.repository.BaseRepository;
import com.tripudiotech.base.util.ReactorUtils;
import com.tripudiotech.datalib.db.DBConstants;
import com.tripudiotech.datalib.db.query.DeleteQuery;
import com.tripudiotech.datalib.db.query.InsertQuery;
import com.tripudiotech.datalib.db.query.MatchByIdQuery;
import com.tripudiotech.datalib.db.query.MatchQuery;
import com.tripudiotech.datalib.db.query.Query;
import com.tripudiotech.datalib.model.OrgRole;
import com.tripudiotech.datalib.model.SysRoot;
import com.tripudiotech.datalib.model.dto.EntityWithPermission;
import com.tripudiotech.datalib.model.dynamic.Relation;
import com.tripudiotech.securitylib.dto.UserInformation;
import io.smallrye.mutiny.Uni;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.SneakyThrows;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import mutiny.zero.flow.adapters.AdaptersToFlow;
import org.apache.commons.lang3.StringUtils;

import jakarta.enterprise.context.ApplicationScoped;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import org.jetbrains.annotations.NotNull;
import reactor.core.publisher.Mono;

/**
 * @author: long.nguyen
 **/
@ApplicationScoped
@FieldDefaults(level = AccessLevel.PRIVATE)
@Slf4j
public class RoleRepository extends BaseRepository<OrgRole> {

    @Override
    public Class<OrgRole> getClazz() {
        return OrgRole.class;
    }

    @Override
    protected OrgRole getDefaultEmptyEntity() {
        return OrgRole.builder()
                .id(null)
                .name(StringUtils.EMPTY)
                .build();
    }

    public Uni<OrgRole> insert(
            @NonNull String tenantId,
            @NonNull UserInformation userInformation,
            @NonNull OrgRole orgRole
    ) {
        InsertQuery<OrgRole> query = InsertQuery.<OrgRole>builder()
                .tenantId(tenantId)
                .entity(orgRole)
                .authoredBy(userInformation.getEmail())
                .build();
        return ReactorUtils.toUni(this.write(List.of(query))
                                .collectList()
                                .map(
                                    result -> this.validateWriteQueryResult(tenantId,
                                        List.of(query), result))
                )
                .flatMap(vr ->
                        Uni.createFrom().publisher(AdaptersToFlow.publisher(getByName(tenantId, orgRole.getName(), userInformation.getEmail())))
                );
    }

    @SneakyThrows
    public Uni<OrgRole> getById(
            @NonNull String tenantId,
            @NonNull String id
    ) {
        return ReactorUtils.toUni(getById(
                        MatchByIdQuery.builder()
                                .model(this.getClazz())
                                .tenantId(tenantId)
                                .returnVar(this.getClazz().getSimpleName())
                                .entityId(id)
                                .build()
                )
        );
    }

    public Uni<Void> delete(
            @NonNull String tenantId,
            @NonNull Set<String> roleIds
    ) {
        if (roleIds.isEmpty()) {
            return Uni.createFrom().voidItem();
        }

        List<Query> queries = new ArrayList<>();
        for (String roleId : roleIds) {
            DeleteQuery<OrgRole> query = DeleteQuery.<OrgRole>builder()
                    .tenantId(tenantId)
                    .entity(
                            OrgRole.builder()
                                    .id(roleId)
                                    .name(StringUtils.EMPTY)
                                    .build()
                    )
                    .build();
            queries.add(query);
        }
        return ReactorUtils.toUni(this.write(queries)
                                .collectList()
                                .map(
                                    result -> this.validateWriteQueryResult(tenantId, queries,
                                        result))
                )
                .flatMap(vr -> Uni.createFrom().voidItem());
    }
    public Mono<List<OrgRole>> getAllRoles(@NotNull String tenantId,
        @NotNull UserInformation userInformation) {
        return this.getAllBy(
                MatchQuery.builder()
                    .tenantId(tenantId)
                    .readerRole(userInformation.getHighestSystemUserRole())
                    .model(OrgRole.class)
                    .offset(0)
                    .limit(Integer.MAX_VALUE)
                    .build(), null
            )
            .collectList();
    }

    public Uni<Void> assignRoleToPerson(@NotNull String tenantId,
        @NotNull UserInformation userInformation,
        @NonNull List<OrgRole> orgRoles,
        @NonNull SysRoot person) {
        List<Query> insertQueries = new ArrayList<>();
        orgRoles.forEach(orgRole -> {
            insertQueries.add(
                InsertQuery.builder()
                    .authoredBy(userInformation.getEmail())
                    .tenantId(tenantId)
                    .entity(
                        Relation.builder()
                            .fromNode(person)
                            .toNode(orgRole)
                            .relationName(
                                DBConstants.RELATION_HAS_ROLE)
                            .build()
                    )
                    .build()
            );
        });
        return ReactorUtils.toUni(this.write(insertQueries)
                .collectList()
                .map(
                    result -> this.validateWriteQueryResult(tenantId,
                        insertQueries, result))
            ).replaceWithVoid();
    }

    public Uni<Void> removeRolesFromPerson(@NotNull String tenantId,
        @NotNull UserInformation userInformation,
        @NonNull List<OrgRole> orgRoles,
        @NonNull SysRoot person) {
        List<Query> deleteQueries = new ArrayList<>();
        orgRoles.forEach(orgRole -> {
            deleteQueries.add(
                DeleteQuery.builder()
                    .tenantId(tenantId)
                    .entity(
                        Relation.builder()
                            .fromNode(person)
                            .toNode(orgRole)
                            .relationName(
                                DBConstants.RELATION_HAS_ROLE)
                            .build()
                    )
                    .build()
            );
        });
        return ReactorUtils.toUni(this.write(deleteQueries)
            .collectList()
            .map(
                result -> this.validateWriteQueryResult(tenantId,
                    deleteQueries, result))
        ).replaceWithVoid();
    }
}
