/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.authservice.repository;

import com.tripudiotech.base.repository.BaseRepository;
import com.tripudiotech.datalib.db.DBClientOptions;
import com.tripudiotech.datalib.db.DBConstants;
import com.tripudiotech.datalib.db.query.ConditionKeyword;
import com.tripudiotech.datalib.db.query.DeleteQuery;
import com.tripudiotech.datalib.db.query.InsertQuery;
import com.tripudiotech.datalib.db.query.MatchQuery;
import com.tripudiotech.datalib.db.query.Query;
import com.tripudiotech.datalib.db.query.RxQueryResult;
import com.tripudiotech.datalib.db.query.SortType;
import com.tripudiotech.datalib.model.SavedFilter;
import com.tripudiotech.datalib.model.SysRoot;
import com.tripudiotech.datalib.pagination.paging.SortBy;
import com.tripudiotech.securitylib.dto.UserInformation;
import io.smallrye.mutiny.Uni;
import java.util.Map;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.SneakyThrows;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONArray;
import org.json.JSONObject;

import jakarta.enterprise.context.ApplicationScoped;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Set;

import org.reactivestreams.FlowAdapters;
import reactor.core.publisher.Mono;

/**
 * @author: long.nguyen
 **/
@ApplicationScoped
@FieldDefaults(level = AccessLevel.PRIVATE)
@Slf4j
public class SavedFilterRepository extends BaseRepository<SavedFilter> {

    @Override
    public Class<SavedFilter> getClazz() {
        return SavedFilter.class;
    }

    @Override
    protected SavedFilter getDefaultEmptyEntity() {
        return SavedFilter.builder()
                .id(null)
                .name(StringUtils.EMPTY)
                .username(StringUtils.EMPTY)
                .type(StringUtils.EMPTY)
                .build();
    }

    public Mono<SavedFilter> insert(
            @NonNull String tenantId,
            @NonNull UserInformation userInformation,
            @NonNull SavedFilter savedFilter
    ) {
        InsertQuery<SavedFilter> query = InsertQuery.<SavedFilter>builder()
                .tenantId(tenantId)
                .entity(savedFilter)
                .authoredBy(userInformation.getEmail())
                .build();
        return this.write(List.of(query))
                .collectList()
                .map(result ->
                        this.validateWriteQueryResult(tenantId,
                                List.of(query), result)
                )
                .flatMap(vr -> getOneBy(tenantId, savedFilter.getName(), savedFilter.getUsername(), savedFilter.getType()));
    }

    public Mono<SavedFilter> getOneBy(
            @NonNull String tenantId,
            @NonNull String name,
            @NonNull String username,
            @NonNull String type
    ) {
        JSONArray jsonArrays = new JSONArray();
        jsonArrays.put(new JSONObject().put(ConditionKeyword.EXACT.getValue(),
                new JSONObject().put(DBConstants.NAME_PROPERTY, name)));
        jsonArrays.put(new JSONObject().put(ConditionKeyword.EXACT.getValue(),
                new JSONObject().put(SavedFilter.Fields.username, username)));
        jsonArrays.put(new JSONObject().put(ConditionKeyword.EXACT.getValue(),
                new JSONObject().put(SavedFilter.Fields.type, type)));
        String jsonCriteria = new JSONObject().put(ConditionKeyword.AND.getValue(), jsonArrays).toString();
        return getAllByCriteriaString(tenantId, jsonCriteria, username, Set.of(SavedFilter.class.getSimpleName()))
                .collectList()
                .map(rs -> {
                    if (rs.isEmpty()) {
                        log.info(
                                "[{}] Unable to find entity. TenantId: {}, Criteria: {}",
                                SavedFilterRepository.class.getSimpleName(),
                                tenantId,
                                jsonCriteria
                        );
                        return getDefaultEmptyEntity();
                    }
                    return rs.get(0);
                });
    }

    @SneakyThrows
    public Mono<SavedFilter> getByIdAndUsername(
            @NonNull String tenantId,
            @NonNull String id,
            @NonNull String username
    ) {
        JSONArray jsonArrays = new JSONArray();
        jsonArrays.put(new JSONObject().put(ConditionKeyword.EXACT.getValue(),
                new JSONObject().put(SysRoot.Fields.id, id)));
        jsonArrays.put(new JSONObject().put(ConditionKeyword.EXACT.getValue(),
                new JSONObject().put(SavedFilter.Fields.username, username)));
        String jsonCriteria = new JSONObject().put(ConditionKeyword.AND.getValue(), jsonArrays).toString();
        return getAllByCriteriaString(tenantId, jsonCriteria, username, Set.of(SavedFilter.class.getSimpleName()))
                .collectList()
                .map(rs -> {
                    if (rs.isEmpty()) {
                        log.info(
                                "[{}] Unable to find entity. TenantId: {}, Criteria: {}",
                                SavedFilterRepository.class.getSimpleName(),
                                tenantId,
                                jsonCriteria
                        );
                        return getDefaultEmptyEntity();
                    }
                    return rs.get(0);
                });
    }

    public Uni<Void> delete(
            @NonNull String tenantId,
            @NonNull Set<String> ids
    ) {
        if (ids.isEmpty()) {
            return Uni.createFrom().voidItem();
        }

        List<Query> queries = new ArrayList<>();
        for (String roleId : ids) {
            DeleteQuery<SavedFilter> query = DeleteQuery.<SavedFilter>builder()
                    .tenantId(tenantId)
                    .entity(
                            SavedFilter.builder()
                                    .id(roleId)
                                    .username(StringUtils.EMPTY)
                                    .type(StringUtils.EMPTY)
                                    .name(StringUtils.EMPTY)
                                    .build()
                    )
                    .build();
            queries.add(query);
        }


        return Uni.createFrom()
                .publisher(
                        FlowAdapters.toFlowPublisher(this.write(queries)
                                .collectList()
                                .map(
                                        result -> this.validateWriteQueryResult(tenantId, queries,
                                                result)))
                )
                .flatMap(vr -> Uni.createFrom().voidItem());
    }

    @SneakyThrows
    public RxQueryResult.Total<SavedFilter> searchEntityWithPaging(
            @NonNull String tenantId,
            @NonNull UserInformation userInformation,
            String jsonCriteria,
            @NonNull Integer offset,
            @NonNull Integer limit,
            Map<String, SortType> sortBy
    ) {
        MatchQuery matchQuery = MatchQuery.builder()
                .model(SavedFilter.class)
                .reader(userInformation.getEmail())
                .offset(offset)
                .limit(limit)
                .sortedBy(sortBy)
                .queryCriterias(Optional.ofNullable(jsonCriteria).filter(StringUtils::isNoneBlank).orElse(null))
                .returnVars(Set.of(SavedFilter.class.getSimpleName()))
                .tenantId(tenantId).build();
        return (RxQueryResult.Total<SavedFilter>) rxDBClient.executeRead(matchQuery, DBClientOptions.builder().includeTotal(true).build());
    }

}
