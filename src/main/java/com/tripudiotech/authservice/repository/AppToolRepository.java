/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.authservice.repository;

import com.tripudiotech.base.repository.BaseRepository;
import com.tripudiotech.datalib.db.DBClientOptions;
import com.tripudiotech.datalib.db.DBConstants;
import com.tripudiotech.datalib.db.query.*;
import com.tripudiotech.datalib.model.AppTool;
import com.tripudiotech.datalib.pagination.paging.SortBy;
import com.tripudiotech.securitylib.dto.UserInformation;
import jakarta.enterprise.context.ApplicationScoped;
import java.util.Map;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.SneakyThrows;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import reactor.core.publisher.Mono;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * @author: long.nguyen
 **/
@ApplicationScoped
@FieldDefaults(level = AccessLevel.PRIVATE)
@Slf4j
public class AppToolRepository extends BaseRepository<AppTool> {

    @Override
    public Class<AppTool> getClazz() {
        return AppTool.class;
    }

    @Override
    protected AppTool getDefaultEmptyEntity() {
        return AppTool.builder()
                .id(null)
                .name(StringUtils.EMPTY)
                .description(StringUtils.EMPTY)
                .build();
    }

    public Mono<AppTool> insert(
            @NonNull String tenantId,
            @NonNull UserInformation userInformation,
            @NonNull AppTool appTool
    ) {
        InsertQuery<AppTool> query = InsertQuery.<AppTool>builder()
                .tenantId(tenantId)
                .entity(appTool)
                .authoredBy(userInformation.getEmail())
                .build();
        return this.write(List.of(query))
                .collectList()
                .map(result ->
                        this.validateWriteQueryResult(tenantId,
                                List.of(query), result)
                )
                .flatMap(vr -> getOneByName(tenantId, appTool.getName(), userInformation));
    }

    public Mono<AppTool> getOneByName(
            @NonNull String tenantId,
            @NonNull String name,
            @NonNull UserInformation userInformation
    ) {
        JSONArray jsonArrays = new JSONArray();
        jsonArrays.put(new JSONObject().put(ConditionKeyword.EXACT.getValue(),
                new JSONObject().put(DBConstants.NAME_PROPERTY, name)));
        String jsonCriteria = new JSONObject().put(ConditionKeyword.AND.getValue(), jsonArrays).toString();
        return getAllByCriteriaString(tenantId, jsonCriteria, userInformation.getEmail(), Set.of(AppTool.class.getSimpleName()))
                .collectList()
                .map(rs -> {
                    if (rs.isEmpty()) {
                        log.info(
                                "[{}] Unable to find AppTool. TenantId: {}, Criteria: {}",
                                AppToolRepository.class.getSimpleName(),
                                tenantId,
                                jsonCriteria
                        );
                        return getDefaultEmptyEntity();
                    }
                    return rs.get(0);
                });
    }

    public Mono<Void> delete(
            @NonNull String tenantId,
            @NonNull Set<String> ids
    ) {
        if (ids.isEmpty()) {
            return Mono.empty().then();
        }

        List<Query> queries = new ArrayList<>();
        for (String id : ids) {
            DeleteQuery<AppTool> query = DeleteQuery.<AppTool>builder()
                    .tenantId(tenantId)
                    .entity(
                            AppTool.builder()
                                    .id(id)
                                    .name(StringUtils.EMPTY)
                                    .build()
                    )
                    .build();
            queries.add(query);
        }
        return  this.write(queries)
            .collectList()
            .map(
                result -> this.validateWriteQueryResult(tenantId, queries,
                    result)).flatMap(voidRs-> Mono.empty().then());
    }

    @SneakyThrows
    public RxQueryResult.Total<AppTool> searchEntityWithPaging(
            @NonNull String tenantId,
            @NonNull UserInformation userInformation,
            String jsonCriteria,
            @NonNull Integer offset,
            @NonNull Integer limit,
            Map<String, SortType> sortBy
    ) {
        MatchQuery matchQuery = MatchQuery.builder()
                .model(AppTool.class)
                .reader(userInformation.getEmail())
                .offset(offset)
                .limit(limit)
                .sortedBy(sortBy)
                .queryCriterias(Optional.ofNullable(jsonCriteria).filter(StringUtils::isNoneBlank).orElse(null))
                .returnVars(Set.of(AppTool.class.getSimpleName()))
                .tenantId(tenantId).build();
        return (RxQueryResult.Total<AppTool>) rxDBClient.executeRead(matchQuery, DBClientOptions.builder().includeTotal(true).build());
    }

}
