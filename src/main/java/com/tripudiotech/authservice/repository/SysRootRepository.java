/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.authservice.repository;

import com.tripudiotech.base.repository.BaseRepository;
import com.tripudiotech.datalib.db.query.DeleteQuery;
import com.tripudiotech.datalib.db.query.InsertQuery;
import com.tripudiotech.datalib.db.query.MatchByIdQuery;
import com.tripudiotech.datalib.db.query.Query;
import com.tripudiotech.datalib.model.OrgRole;
import com.tripudiotech.datalib.model.SysRoot;
import com.tripudiotech.securitylib.dto.UserInformation;
import io.smallrye.mutiny.Uni;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.SneakyThrows;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import jakarta.enterprise.context.ApplicationScoped;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * @author: long.nguyen
 **/
@ApplicationScoped
@FieldDefaults(level = AccessLevel.PRIVATE)
@Slf4j
public class SysRootRepository extends BaseRepository<SysRoot> {

    @Override
    public Class<SysRoot> getClazz() {
        return SysRoot.class;
    }

    @Override
    protected SysRoot getDefaultEmptyEntity() {
        return SysRoot.builder()
                .id(null)
                .build();
    }

}
