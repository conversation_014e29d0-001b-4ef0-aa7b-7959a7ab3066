/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.authservice.utils;

import com.tripudiotech.securitylib.dto.LoginRequestDTO;

import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Optional;

public class GenericHelper {
    public static LoginRequestDTO decodeAuthorizationBase64(String authBase64InBase64) {
        return Optional.ofNullable(authBase64InBase64)
                .map(authString -> {
                    String base64Credentials = authString.substring("Basic".length()).trim();
                    byte[] credDecoded = Base64.getDecoder().decode(base64Credentials);
                    String credentials = new String(credDecoded, StandardCharsets.UTF_8);
                    // credentials = username:password
                    final String[] values = credentials.split(":", 2);
                    return LoginRequestDTO.builder()
                            .username(values[0])
                            .password(values[1])
                            .build();
                })
                .orElse(null);
    }
}
