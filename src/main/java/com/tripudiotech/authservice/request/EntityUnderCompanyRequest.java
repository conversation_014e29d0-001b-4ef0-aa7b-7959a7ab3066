/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.authservice.request;

import com.tripudiotech.base.client.dto.request.CreateEntityRequest;
import com.tripudiotech.datalib.db.DBConstants;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;
import lombok.experimental.FieldDefaults;
import lombok.experimental.FieldNameConstants;
import lombok.experimental.SuperBuilder;

import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

@SuperBuilder(toBuilder = true)
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@NoArgsConstructor
@FieldNameConstants
@AllArgsConstructor
public class EntityUnderCompanyRequest {

   @NonNull
   @Builder.Default
   Map<String, Object> attributes = new HashMap<>();

   @Builder.Default
   Set<CreateEntityRequest.RelationRequest> relations = new HashSet<>();

   public EntityUnderCompanyRequest(@NonNull String companyId,
                                    @NonNull Map<String,Object> properties) {
      this.setAttributes(Collections.unmodifiableMap(properties));
      this. relations = new HashSet<>();
      relations.add(
              CreateEntityRequest.RelationRequest.builder()
                      .name(DBConstants.RELATION_WORKS_FOR)
                      .entityType("Company")
                      .entityId(companyId)
                      .build()
      );
      relations.add(
              CreateEntityRequest.RelationRequest.builder()
                      .name(DBConstants.RELATION_OWNED_BY)
                      .entityType("Agent")
                      .entityId(companyId)
                      .build()
      );
   }
}
