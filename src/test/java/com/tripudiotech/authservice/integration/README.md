# End-to-End Integration Tests

This directory contains end-to-end integration tests for the auth service, specifically focusing on the createUser API.

## Test Structure

### Main Test Class
- `CreateUserEndToEndTest.java` - Main test class that verifies the complete user creation workflow

### Utility Classes
- `AuthenticationHelper.java` - Handles authentication with the identity service
- `TestDataHelper.java` - Manages test data creation, verification, and cleanup
- `UserTestData.java` - Data container for user test data

## Test Configuration

The tests use `src/test/resources/application.yml` for configuration. Key configuration properties:

```yaml
test:
  identity:
    server:
      url: https://identity-stage.glideyoke.com
    client:
      id: sp-services
    realm: ui-qa-stage
    username: superadmin
    password: UXWnVXxQrLbYBsKNxNmz
  company:
    id: test-company-id
```

## Running the Tests

### Prerequisites
1. Ensure the auth service is running locally on port 8081 (or update the configuration)
2. Ensure the identity service is accessible at the configured URL
3. Ensure the test database is available

### Run with Maven
```bash
# Run all tests
mvn test

# Run only integration tests
mvn test -Dtest="*EndToEndTest"

# Run specific test
mvn test -Dtest="CreateUserEndToEndTest"
```

### Run with IDE
1. Right-click on the test class
2. Select "Run CreateUserEndToEndTest"

## Test Flow

The `CreateUserEndToEndTest` follows this workflow:

1. **Setup** (`@BeforeEach`)
   - Authenticate with identity service to get access token
   - Get or create test company

2. **Test Execution**
   - Generate random test data (user with groups)
   - Call createUser API
   - Verify user entity creation
   - Verify auth server user creation
   - Verify group creation and assignment

3. **Cleanup** (`@AfterEach`)
   - Delete created users
   - Delete created auth users
   - Delete created groups (optional)

## Test Data

The test generates random test data to avoid conflicts:

```json
{
  "username": "<EMAIL>",
  "firstName": "Test",
  "lastName": "User12345678",
  "description": "This is test user created for integration test",
  "properties": {
    "dob": "1994-05-15",
    "email": "<EMAIL>",
    "firstName": "Test",
    "lastName": "User12345678",
    "name": "Test User12345678"
  },
  "groups": [
    {
      "value": "2025.01.15 14.30 Group 1"
    },
    {
      "value": "2025.01.15 14.30 Group 2"
    },
    {
      "value": "2025.01.15 14.30 Group 3"
    }
  ]
}
```

## Customization

### Environment-Specific Configuration
You can override configuration for different environments:

```bash
# For staging environment
mvn test -Dtest.identity.server.url=https://identity-staging.example.com

# For different credentials
mvn test -Dtest.identity.username=testuser -Dtest.identity.password=testpass
```

### Adding New Test Scenarios
1. Create new test methods in `CreateUserEndToEndTest`
2. Use `TestDataHelper.generateUserTestData()` for random data
3. Use `UserTestData.createSample()` for consistent test data
4. Use `UserTestData.createWithoutGroups()` for testing without groups

## Troubleshooting

### Common Issues

1. **Authentication Failed**
   - Check identity service URL and credentials
   - Verify network connectivity
   - Check if the realm and client ID are correct

2. **Test Company Not Found**
   - Update `test.company.id` in configuration
   - Ensure the company exists in the system

3. **Database Connection Issues**
   - Check database URL and credentials
   - Ensure test database is running

4. **Port Conflicts**
   - Update `quarkus.http.test-port` in test configuration
   - Ensure no other service is using the test port

### Debug Mode
Run tests with debug logging:

```bash
mvn test -Dquarkus.log.category."com.tripudiotech.authservice".level=DEBUG
```

## Future Enhancements

1. **Implement Missing APIs**
   - Group search API
   - User-group assignment verification API
   - Auth user deletion API

2. **Add More Test Scenarios**
   - User creation without groups
   - User creation with invalid data
   - User creation with existing email
   - Bulk user creation

3. **Performance Testing**
   - Add performance benchmarks
   - Test with large datasets
   - Concurrent user creation tests

4. **Test Data Management**
   - Implement test data fixtures
   - Add test data seeding
   - Improve cleanup strategies
