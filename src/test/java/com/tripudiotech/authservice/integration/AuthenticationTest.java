/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.authservice.integration;

import com.tripudiotech.authservice.integration.util.AuthenticationHelper;
import io.quarkus.test.junit.QuarkusTest;
import jakarta.inject.Inject;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Simple test to verify authentication is working.
 * This test can be run independently to check if the identity service is accessible.
 */
@QuarkusTest
class AuthenticationTest {

    @Inject
    AuthenticationHelper authHelper;

    @Test
    @DisplayName("Should successfully obtain access token from identity service")
    void shouldObtainAccessToken() {
        // When
        String accessToken = authHelper.getAccessToken();

        // Then
        assertNotNull(accessToken, "Access token should not be null");
        assertFalse(accessToken.trim().isEmpty(), "Access token should not be empty");
        assertTrue(accessToken.length() > 50, "Access token should be a reasonable length");
        
        System.out.println("✅ Successfully obtained access token");
        System.out.println("Token length: " + accessToken.length());
        System.out.println("Token preview: " + accessToken.substring(0, Math.min(50, accessToken.length())) + "...");
    }

    @Test
    @DisplayName("Should cache and reuse access token")
    void shouldCacheAccessToken() {
        // When - Get token twice
        String token1 = authHelper.getAccessToken();
        String token2 = authHelper.getAccessToken();

        // Then - Should be the same token (cached)
        assertEquals(token1, token2, "Tokens should be the same when cached");
        
        System.out.println("✅ Token caching is working correctly");
    }

    @Test
    @DisplayName("Should refresh access token when forced")
    void shouldRefreshAccessToken() {
        // Given
        String originalToken = authHelper.getAccessToken();

        // When - Force refresh
        String refreshedToken = authHelper.refreshAccessToken();

        // Then - Should get a new token
        assertNotNull(refreshedToken, "Refreshed token should not be null");
        assertFalse(refreshedToken.trim().isEmpty(), "Refreshed token should not be empty");
        
        // Note: Tokens might be the same if they haven't expired, so we just verify we got a valid token
        System.out.println("✅ Token refresh is working correctly");
        System.out.println("Original token length: " + originalToken.length());
        System.out.println("Refreshed token length: " + refreshedToken.length());
    }
}
