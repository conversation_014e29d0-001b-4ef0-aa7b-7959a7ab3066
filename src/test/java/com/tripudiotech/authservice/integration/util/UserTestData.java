/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.authservice.integration.util;

import lombok.AccessLevel;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.util.HashMap;
import java.util.Map;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Test data container for user creation tests.
 * Provides convenient methods to generate test data and convert to API request format.
 */
@Data
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
public class UserTestData {
    
    String username;
    String email;
    String firstName;
    String lastName;
    String description;
    String[] groupNames;

    /**
     * Converts the test data to AccountRequest format for API calls.
     * Note: Since AccountRequest is from an external library, we return a Map
     * that can be serialized to JSON with the same structure.
     *
     * @return Map representing the AccountRequest
     */
    public Map<String, Object> getAccountRequest() {
        Map<String, Object> request = new HashMap<>();
        
        request.put("username", username);
        request.put("firstName", firstName);
        request.put("lastName", lastName);
        request.put("description", description);
        
        // Properties map
        Map<String, Object> properties = new HashMap<>();
        properties.put("dob", "1994-05-15");
        properties.put("email", email);
        properties.put("firstName", firstName);
        properties.put("lastName", lastName);
        properties.put("name", firstName + " " + lastName);
        
        request.put("properties", properties);
        
        // Groups array
        if (groupNames != null && groupNames.length > 0) {
            List<Map<String, String>> groups = Arrays.stream(groupNames)
                    .map(groupName -> {
                        Map<String, String> group = new HashMap<>();
                        group.put("value", groupName);
                        return group;
                    })
                    .collect(Collectors.toList());
            
            request.put("groups", groups);
        }
        
        return request;
    }

    /**
     * Gets the list of group names.
     *
     * @return list of group names
     */
    public List<String> getGroupNames() {
        return groupNames != null ? Arrays.asList(groupNames) : List.of();
    }

    /**
     * Creates a sample test data instance for quick testing.
     *
     * @return UserTestData with sample values
     */
    public static UserTestData createSample() {
        return UserTestData.builder()
                .username("<EMAIL>")
                .email("<EMAIL>")
                .firstName("Sample")
                .lastName("User")
                .description("Sample test user for integration testing")
                .groupNames(new String[]{
                        "2025.01.01 12.00 Sample Group 1",
                        "2025.01.01 12.00 Sample Group 2"
                })
                .build();
    }

    /**
     * Creates test data with custom email domain.
     *
     * @param emailDomain the email domain to use
     * @return UserTestData with custom email domain
     */
    public static UserTestData createWithEmailDomain(String emailDomain) {
        String timestamp = String.valueOf(System.currentTimeMillis());
        String email = "test_" + timestamp + "@" + emailDomain;
        
        return UserTestData.builder()
                .username(email)
                .email(email)
                .firstName("Test")
                .lastName("User" + timestamp.substring(timestamp.length() - 4))
                .description("Test user with custom email domain")
                .groupNames(new String[]{
                        "Test Group " + timestamp
                })
                .build();
    }

    /**
     * Creates test data without groups.
     *
     * @return UserTestData without groups
     */
    public static UserTestData createWithoutGroups() {
        String timestamp = String.valueOf(System.currentTimeMillis());
        String email = "test_no_groups_" + timestamp + "@example.com";
        
        return UserTestData.builder()
                .username(email)
                .email(email)
                .firstName("NoGroups")
                .lastName("User")
                .description("Test user without groups")
                .groupNames(new String[0])
                .build();
    }
}
