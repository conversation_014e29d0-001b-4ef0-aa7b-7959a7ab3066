/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.authservice.integration;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tripudiotech.authservice.integration.util.AuthenticationHelper;
import com.tripudiotech.authservice.integration.util.TestDataHelper;
import com.tripudiotech.authservice.integration.util.UserTestData;
import io.quarkus.test.junit.QuarkusTest;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import jakarta.inject.Inject;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInfo;

import java.util.ArrayList;
import java.util.List;

import static io.restassured.RestAssured.given;
import static org.hamcrest.Matchers.*;
import static org.junit.jupiter.api.Assertions.*;

/**
 * End-to-end test for the createUser API.
 *
 * This test verifies the complete user creation workflow:
 * 1. Authenticates with the identity service
 * 2. Creates a user with groups
 * 3. Verifies user entity creation
 * 4. Verifies auth server user creation
 * 5. Verifies group creation and assignment
 * 6. Cleans up test data
 */
@QuarkusTest
class CreateUserEndToEndTest {

    @Inject
    AuthenticationHelper authHelper;

    @Inject
    TestDataHelper testDataHelper;

    @Inject
    ObjectMapper objectMapper;

    @ConfigProperty(name = "quarkus.http.test-port", defaultValue = "8081")
    int testPort;

    private String accessToken;
    private String companyId;
    private String baseUrl;
    private List<String> createdUserIds = new ArrayList<>();
    private List<String> createdGroupIds = new ArrayList<>();
    private List<String> createdAuthUserIds = new ArrayList<>();

    @BeforeEach
    void setUp(TestInfo testInfo) {
        // Configure base URL for local API calls
        baseUrl = "http://localhost:" + testPort;

        // Get authentication token
        accessToken = authHelper.getAccessToken();
        assertNotNull(accessToken, "Failed to obtain access token");

        // Configure test data helper with base URL
        testDataHelper.setBaseUrl(baseUrl);

        // Set up test company (you may need to create or use existing company)
        companyId = testDataHelper.getOrCreateTestCompany();
        assertNotNull(companyId, "Failed to get test company ID");

        System.out.println("Starting test: " + testInfo.getDisplayName());
        System.out.println("Using base URL: " + baseUrl);
    }

    @AfterEach
    void tearDown() {
        // Clean up created test data
        cleanupTestData();
        System.out.println("Test cleanup completed");
    }

    @Test
    @DisplayName("Should create user with groups and verify all entities")
    void shouldCreateUserWithGroupsAndVerifyAllEntities() throws Exception {
        // Given
        UserTestData testData = testDataHelper.generateUserTestData();

        // When - Create user
        Response createResponse = given()
                .baseUri(baseUrl)
                .header("Authorization", "Bearer " + accessToken)
                .header("X-Tenant-Id", "master")
                .contentType(ContentType.JSON)
                .body(testData.getAccountRequest())
                .when()
                .post("/company/{companyId}/user", companyId)
                .then()
                .statusCode(200)
                .body("id", notNullValue())
                .body("properties.email", equalTo(testData.getEmail()))
                .body("properties.firstName", equalTo(testData.getFirstName()))
                .body("properties.lastName", equalTo(testData.getLastName()))
                .extract()
                .response();

        // Extract created user ID
        JsonNode responseJson = objectMapper.readTree(createResponse.asString());
        String createdUserId = responseJson.get("id").asText();
        createdUserIds.add(createdUserId);

        // Then - Verify user entity was created
        verifyUserEntityCreated(createdUserId, testData);

        // Verify auth server user was created
        String authUserId = verifyAuthServerUserCreated(testData);
        createdAuthUserIds.add(authUserId);

        // Verify groups were created and user assigned
        verifyGroupsCreatedAndAssigned(testData, createdUserId);

        System.out.println("User creation test completed successfully");
    }

    private void verifyUserEntityCreated(String userId, UserTestData testData) {
        // Verify user entity exists and has correct properties
        given()
                .baseUri(baseUrl)
                .header("Authorization", "Bearer " + accessToken)
                .header("X-Tenant-Id", "master")
                .when()
                .get("/user/{userId}", userId)
                .then()
                .statusCode(200)
                .body("id", equalTo(userId))
                .body("properties.email", equalTo(testData.getEmail()))
                .body("properties.firstName", equalTo(testData.getFirstName()))
                .body("properties.lastName", equalTo(testData.getLastName()));

        System.out.println("✓ User entity verified: " + userId);
    }

    private String verifyAuthServerUserCreated(UserTestData testData) {
        // This would typically involve calling Keycloak API or checking through your service
        // For now, we'll assume the auth user ID is returned in the response or can be retrieved
        // You may need to implement a method to get auth user by email

        // Placeholder - implement based on your auth service capabilities
        String authUserId = testDataHelper.getAuthUserIdByEmail(testData.getEmail());
        assertNotNull(authUserId, "Auth server user should be created");

        System.out.println("✓ Auth server user verified: " + authUserId);
        return authUserId;
    }

    private void verifyGroupsCreatedAndAssigned(UserTestData testData, String userId) {
        for (String groupName : testData.getGroupNames()) {
            // Check if group exists or was created
            String groupId = testDataHelper.getOrCreateGroupByName(groupName);
            if (groupId != null) {
                createdGroupIds.add(groupId);
            }

            // Verify user is assigned to the group
            // This depends on your API structure - you may need to implement group membership check
            boolean isAssigned = testDataHelper.isUserAssignedToGroup(userId, groupName);
            assertTrue(isAssigned, "User should be assigned to group: " + groupName);

            System.out.println("✓ Group assignment verified: " + groupName);
        }
    }

    private void cleanupTestData() {
        try {
            // Clean up created users
            for (String userId : createdUserIds) {
                testDataHelper.deleteUser(userId);
            }

            // Clean up created auth users
            for (String authUserId : createdAuthUserIds) {
                testDataHelper.deleteAuthUser(authUserId);
            }

            // Clean up created groups (optional - groups might be reused)
            for (String groupId : createdGroupIds) {
                testDataHelper.deleteGroup(groupId);
            }

            System.out.println("✓ Test data cleanup completed");
        } catch (Exception e) {
            System.err.println("Error during cleanup: " + e.getMessage());
            // Log but don't fail the test due to cleanup issues
        }
    }
}
