/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.authservice.service.context;

import com.tripudiotech.base.client.dto.request.AccountRequest;
import com.tripudiotech.datalib.model.dto.EntityWithPermission;
import com.tripudiotech.securitylib.dto.UserInformation;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import java.util.HashMap;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class demonstrating the benefits of the UserCreationContext pattern.
 * This shows how the context object simplifies state management and testing.
 */
class UserCreationContextTest {

    @Test
    @DisplayName("Should create context with required fields")
    void shouldCreateContextWithRequiredFields() {
        // Given
        String tenantId = "test-tenant";
        String companyId = "company-123";
        String resolvedEmail = "<EMAIL>";
        String token = "Bearer token123";

        AccountRequest request = AccountRequest.builder()
                .username("<EMAIL>")
                .firstName("John")
                .lastName("Doe")
                .properties(new HashMap<>())
                .build();

        UserInformation userInfo = UserInformation.builder()
                .username("admin")
                .email("<EMAIL>")
                .enabled(true)
                .build();

        // When
        UserCreationContext context = UserCreationContext.builder()
                .tenantId(tenantId)
                .companyId(companyId)
                .resolvedEmail(resolvedEmail)
                .token(token)
                .request(request)
                .userInformation(userInfo)
                .build();

        // Then
        assertEquals(tenantId, context.getTenantId());
        assertEquals(companyId, context.getCompanyId());
        assertEquals(resolvedEmail, context.getResolvedEmail());
        assertEquals(token, context.getToken());
        assertEquals(request, context.getRequest());
        assertEquals(userInfo, context.getUserInformation());
        assertNull(context.getCompanyEntity());
        assertNull(context.getCreatedEntity());
        assertNull(context.getAuthServerId());
    }

    @Test
    @DisplayName("Should create new context with company entity")
    void shouldCreateNewContextWithCompanyEntity() {
        // Given
        UserCreationContext originalContext = createBasicContext();
        EntityWithPermission companyEntity = new EntityWithPermission();
        companyEntity.setId("company-entity-123");

        // When
        UserCreationContext newContext = originalContext.withCompanyEntity(companyEntity);

        // Then
        assertNotSame(originalContext, newContext); // Immutability check
        assertEquals(originalContext.getTenantId(), newContext.getTenantId());
        assertEquals(originalContext.getCompanyId(), newContext.getCompanyId());
        assertEquals(companyEntity, newContext.getCompanyEntity());
        assertNull(originalContext.getCompanyEntity()); // Original unchanged
    }

    @Test
    @DisplayName("Should create new context with created entity")
    void shouldCreateNewContextWithCreatedEntity() {
        // Given
        UserCreationContext originalContext = createBasicContext();
        EntityWithPermission createdEntity = new EntityWithPermission();
        createdEntity.setId("user-entity-123");

        // When
        UserCreationContext newContext = originalContext.withCreatedEntity(createdEntity);

        // Then
        assertNotSame(originalContext, newContext);
        assertEquals(createdEntity, newContext.getCreatedEntity());
        assertNull(originalContext.getCreatedEntity());
    }

    @Test
    @DisplayName("Should create new context with auth server ID")
    void shouldCreateNewContextWithAuthServerId() {
        // Given
        UserCreationContext originalContext = createBasicContext();
        String authServerId = "auth-server-123";

        // When
        UserCreationContext newContext = originalContext.withAuthServerId(authServerId);

        // Then
        assertNotSame(originalContext, newContext);
        assertEquals(authServerId, newContext.getAuthServerId());
        assertNull(originalContext.getAuthServerId());
    }

    @Test
    @DisplayName("Should check if context is ready for permission assignment")
    void shouldCheckIfReadyForPermissionAssignment() {
        // Given
        UserCreationContext context = createBasicContext();

        // When - context without entities
        boolean readyWithoutEntities = context.isReadyForPermissionAssignment();

        // Then
        assertFalse(readyWithoutEntities);

        // When - context with entities
        EntityWithPermission entity = new EntityWithPermission();
        entity.setId("user-123");

        UserCreationContext contextWithEntities = context
                .withCreatedEntity(entity)
                .withAuthServerId("auth-123");

        boolean readyWithEntities = contextWithEntities.isReadyForPermissionAssignment();

        // Then
        assertTrue(readyWithEntities);
    }

    @Test
    @DisplayName("Should check if context is complete")
    void shouldCheckIfContextIsComplete() {
        // Given
        UserCreationContext context = createBasicContext();

        // When - incomplete context
        boolean incompleteContext = context.isComplete();

        // Then
        assertFalse(incompleteContext);

        // When - complete context
        EntityWithPermission companyEntity = new EntityWithPermission();
        companyEntity.setId("company-123");

        EntityWithPermission userEntity = new EntityWithPermission();
        userEntity.setId("user-123");

        UserCreationContext completeContext = context
                .withCompanyEntity(companyEntity)
                .withCreatedEntity(userEntity)
                .withAuthServerId("auth-123");

        boolean complete = completeContext.isComplete();

        // Then
        assertTrue(complete);
    }

    @Test
    @DisplayName("Should demonstrate immutability benefits")
    void shouldDemonstrateImmutabilityBenefits() {
        // Given
        UserCreationContext originalContext = createBasicContext();

        // When - creating multiple derived contexts
        EntityWithPermission companyEntity = new EntityWithPermission();
        companyEntity.setId("company-123");

        EntityWithPermission userEntity = new EntityWithPermission();
        userEntity.setId("user-123");

        UserCreationContext step1 = originalContext.withCompanyEntity(companyEntity);
        UserCreationContext step2 = step1.withCreatedEntity(userEntity);
        UserCreationContext step3 = step2.withAuthServerId("auth-123");

        // Then - each step creates a new immutable context
        assertNotSame(originalContext, step1);
        assertNotSame(step1, step2);
        assertNotSame(step2, step3);

        // Original context remains unchanged
        assertNull(originalContext.getCompanyEntity());
        assertNull(originalContext.getCreatedEntity());
        assertNull(originalContext.getAuthServerId());

        // Each step builds upon the previous
        assertEquals(companyEntity, step1.getCompanyEntity());
        assertEquals(companyEntity, step2.getCompanyEntity());
        assertEquals(userEntity, step2.getCreatedEntity());
        assertEquals("auth-123", step3.getAuthServerId());

        // This demonstrates:
        // - Immutability prevents accidental state changes
        // - Clear workflow progression
        // - Thread safety
        // - Easy to reason about state transitions
    }

    private UserCreationContext createBasicContext() {
        AccountRequest request = AccountRequest.builder()
                .username("<EMAIL>")
                .firstName("John")
                .lastName("Doe")
                .properties(new HashMap<>())
                .build();

        UserInformation userInfo = UserInformation.builder()
                .username("admin")
                .email("<EMAIL>")
                .enabled(true)
                .build();

        return UserCreationContext.builder()
                .tenantId("test-tenant")
                .companyId("company-123")
                .resolvedEmail("<EMAIL>")
                .token("Bearer token123")
                .request(request)
                .userInformation(userInfo)
                .build();
    }
}
