/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.authservice.service;

import com.tripudiotech.authservice.model.UserProtectionInfo;
import com.tripudiotech.base.configuration.exception.PermissionDeniedException;
import com.tripudiotech.securitylib.dto.UserInformation;
import io.quarkus.test.junit.QuarkusTest;
import jakarta.inject.Inject;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import java.util.List;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class demonstrating the improved user protection logic.
 * Shows how the refactored code is more testable and maintainable.
 */
@QuarkusTest
class UserProtectionServiceTest {

    @Inject
    UserProtectionService userProtectionService;

    @Test
    @DisplayName("Should protect admin user from status changes")
    void shouldProtectAdminUserFromStatusChanges() {
        // Given
        UserInformation adminUser = createUserWithUsername("admin");
        String tenantId = "test-tenant";

        // When & Then
        assertThrows(PermissionDeniedException.class, () ->
            userProtectionService.validateUserStatusChange(tenantId, adminUser, false)
        );
    }

    @Test
    @DisplayName("Should protect superadmin user from status changes")
    void shouldProtectSuperadminUserFromStatusChanges() {
        // Given
        UserInformation superadminUser = createUserWithUsername("superadmin");
        String tenantId = "test-tenant";

        // When & Then
        assertThrows(PermissionDeniedException.class, () ->
            userProtectionService.validateUserStatusChange(tenantId, superadminUser, true)
        );
    }

    @Test
    @DisplayName("Should allow regular user status changes")
    void shouldAllowRegularUserStatusChanges() {
        // Given
        UserInformation regularUser = createUserWithUsernameAndStatus("john.doe", true);
        String tenantId = "test-tenant";

        // When & Then - should not throw exception
        assertDoesNotThrow(() ->
            userProtectionService.validateUserStatusChange(tenantId, regularUser, false)
        );
    }

    @Test
    @DisplayName("Should protect user with protected roles")
    void shouldProtectUserWithProtectedRoles() {
        // Given
        UserInformation userWithProtectedRole = createUserWithRoles("john.doe", List.of("SUPER_ADMIN", "USER"));
        String tenantId = "test-tenant";

        // When & Then
        assertThrows(PermissionDeniedException.class, () ->
            userProtectionService.validateUserCanBeUpdated(tenantId, userWithProtectedRole)
        );
    }

    @Test
    @DisplayName("Should allow updates for users without protected roles")
    void shouldAllowUpdatesForUsersWithoutProtectedRoles() {
        // Given
        UserInformation regularUser = createUserWithRoles("john.doe", List.of("USER", "MANAGER"));
        String tenantId = "test-tenant";

        // When & Then - should not throw exception
        assertDoesNotThrow(() ->
            userProtectionService.validateUserCanBeUpdated(tenantId, regularUser)
        );
    }

    @Test
    @DisplayName("Should detect protected users correctly")
    void shouldDetectProtectedUsersCorrectly() {
        // Given
        UserInformation adminUser = createUserWithUsername("admin");
        UserInformation protectedRoleUser = createUserWithRoles("john.doe", List.of("SUPER_ADMIN"));
        UserInformation regularUser = createUserWithUsername("jane.doe");

        // When & Then
        assertTrue(userProtectionService.isUserProtected(adminUser));
        assertTrue(userProtectionService.isUserProtected(protectedRoleUser));
        assertFalse(userProtectionService.isUserProtected(regularUser));
    }

    @Test
    @DisplayName("Should provide detailed protection information")
    void shouldProvideDetailedProtectionInformation() {
        // Given
        UserInformation adminUser = createUserWithUsername("admin");
        UserInformation protectedRoleUser = createUserWithRoles("john.doe", List.of("SUPER_ADMIN"));
        UserInformation regularUser = createUserWithUsername("jane.doe");

        // When
        UserProtectionInfo adminInfo = userProtectionService.getUserProtectionInfo(adminUser);
        UserProtectionInfo roleInfo = userProtectionService.getUserProtectionInfo(protectedRoleUser);
        UserProtectionInfo regularInfo = userProtectionService.getUserProtectionInfo(regularUser);

        // Then
        assertTrue(adminInfo.isProtected());
        assertEquals(UserProtectionInfo.ProtectionReason.USERNAME_BASED, adminInfo.getPrimaryReason());
        assertTrue(adminInfo.isOperationRestricted(UserProtectionInfo.RestrictedOperation.STATUS_CHANGE));

        assertTrue(roleInfo.isProtected());
        assertEquals(UserProtectionInfo.ProtectionReason.ROLE_BASED, roleInfo.getPrimaryReason());
        assertTrue(roleInfo.isOperationRestricted(UserProtectionInfo.RestrictedOperation.UPDATE));

        assertFalse(regularInfo.isProtected());
        assertEquals(UserProtectionInfo.ProtectionReason.NOT_PROTECTED, regularInfo.getPrimaryReason());
        assertFalse(regularInfo.isOperationRestricted(UserProtectionInfo.RestrictedOperation.DELETE));
    }

    @Test
    @DisplayName("Should handle case-insensitive username matching")
    void shouldHandleCaseInsensitiveUsernameMatching() {
        // Given
        UserInformation upperCaseAdmin = createUserWithUsername("ADMIN");
        UserInformation mixedCaseAdmin = createUserWithUsername("Admin");
        String tenantId = "test-tenant";

        // When & Then
        assertTrue(userProtectionService.isUserProtected(upperCaseAdmin));
        assertTrue(userProtectionService.isUserProtected(mixedCaseAdmin));

        assertThrows(PermissionDeniedException.class, () ->
            userProtectionService.validateUserCanBeDeleted(tenantId, upperCaseAdmin)
        );
    }

    @Test
    @DisplayName("Should validate status change logic correctly")
    void shouldValidateStatusChangeLogicCorrectly() {
        // Given
        String tenantId = "test-tenant";

        UserInformation activeUser = createUserWithUsernameAndStatus("john.doe", true);
        UserInformation inactiveUser = createUserWithUsernameAndStatus("jane.doe", false);

        // When & Then - trying to enable already active user
        assertThrows(PermissionDeniedException.class, () ->
            userProtectionService.validateUserStatusChange(tenantId, activeUser, true)
        );

        // When & Then - trying to disable already inactive user
        assertThrows(PermissionDeniedException.class, () ->
            userProtectionService.validateUserStatusChange(tenantId, inactiveUser, false)
        );

        // When & Then - valid operations should not throw
        assertDoesNotThrow(() ->
            userProtectionService.validateUserStatusChange(tenantId, activeUser, false)
        );
        assertDoesNotThrow(() ->
            userProtectionService.validateUserStatusChange(tenantId, inactiveUser, true)
        );
    }

    @Test
    @DisplayName("Should demonstrate improved maintainability over hardcoded checks")
    void shouldDemonstrateImprovedMaintainability() {
        // Before refactoring: Adding a new protected username required:
        // 1. Finding all hardcoded checks in the codebase
        // 2. Updating each check individually
        // 3. Risk of missing some checks
        // 4. No central configuration

        // After refactoring: Adding a new protected username requires:
        // 1. Update application.yml configuration
        // 2. All protection logic automatically uses the new configuration
        // 3. Centralized, testable logic

        // This test demonstrates the configuration-driven approach
        Set<String> protectedUsernames = userProtectionService.getProtectedUsernames();
        Set<String> protectedRoles = userProtectionService.getProtectedRoles();

        assertTrue(protectedUsernames.contains("admin"));
        assertTrue(protectedUsernames.contains("superadmin"));
        assertTrue(protectedRoles.contains("SUPER_ADMIN"));
        assertTrue(protectedRoles.contains("SYSTEM_ADMIN"));

        // Configuration is immutable and safe to expose
        assertThrows(UnsupportedOperationException.class, () ->
            protectedUsernames.add("new-admin")
        );
    }

    private UserInformation createUserWithUsername(String username) {
        return UserInformation.builder()
                .username(username)
                .email(username + "@example.com")
                .enabled(true)
                .build();
    }

    private UserInformation createUserWithUsernameAndStatus(String username, boolean enabled) {
        return UserInformation.builder()
                .username(username)
                .email(username + "@example.com")
                .enabled(enabled)
                .build();
    }

    private UserInformation createUserWithRoles(String username, List<String> roles) {
        return UserInformation.builder()
                .username(username)
                .email(username + "@example.com")
                .enabled(true)
                .roles(Set.copyOf(roles))
                .build();
    }
}
