/*
 * Copyright (c) 2023-2025 Glide Systems, Inc.
 * 19925 Stevens Creek Blvd, Cupertino, CA 95014
 * All rights reserved.
 * This software (the "Software") is provided pursuant to the license agreement you entered into with Glide Systems, Inc.
 * (the "License Agreement"). The Software is the confidential and proprietary information
 * of Glide Systems, Inc., and you shall use it only in accordance with the terms and conditions of the License Agreement.
 * THE SOFTWARE IS PROVIDED "AS IS" AND "AS AVAILABLE."  GLIDE SYSTEMS, INC.
 * MAKES NO WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING
 * BUT NOT LIMITED TO THE IMPLIED WARRANTIES AND CONDITIONS OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT.
 */

package com.tripudiotech.authservice.service;

import com.tripudiotech.authservice.service.context.UserCreationContext;
import com.tripudiotech.base.client.dto.request.AccountRequest;
import com.tripudiotech.datalib.client.EntityRepository;
import com.tripudiotech.datalib.model.dto.EntityWithPermission;
import com.tripudiotech.securitylib.dto.UserInformation;
import com.tripudiotech.securitylib.service.SecurityProviderService;
import com.tripudiotech.securitylib.service.SecurityProviderServiceFactory;
import io.quarkus.test.junit.QuarkusTest;
import io.quarkus.test.junit.mockito.InjectMock;
import io.smallrye.mutiny.Uni;
import io.smallrye.mutiny.helpers.test.UniAssertSubscriber;
import jakarta.inject.Inject;
import jakarta.ws.rs.core.Response;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.util.HashMap;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Test class for UserCreationRollbackService.
 * Tests all rollback scenarios to ensure data consistency during failures.
 */
@QuarkusTest
class UserCreationRollbackServiceTest {

    @Inject
    UserCreationRollbackService rollbackService;

    @InjectMock
    EntityRepository entityRepository;

    @InjectMock
    SecurityProviderServiceFactory securityProviderServiceFactory;

    @InjectMock
    SecurityProviderService securityProviderService;

    @InjectMock
    AsyncConfigurationService asyncConfigurationService;

    private UserCreationContext testContext;
    private EntityWithPermission testEntity;

    @BeforeEach
    void setUp() {
        // Setup mock responses
        when(securityProviderServiceFactory.getDefaultAuthenticateService())
                .thenReturn(securityProviderService);
        when(asyncConfigurationService.getCustomExecutor())
                .thenReturn(Runnable::run); // Execute synchronously for testing

        // Create test entity
        testEntity = new EntityWithPermission();
        testEntity.setId("test-entity-123");
        testEntity.setProperties(new HashMap<>());

        // Create test context
        testContext = UserCreationContext.builder()
                .tenantId("test-tenant")
                .companyId("company-123")
                .resolvedEmail("<EMAIL>")
                .token("Bearer test-token")
                .request(AccountRequest.builder()
                        .username("<EMAIL>")
                        .firstName("John")
                        .lastName("Doe")
                        .properties(new HashMap<>())
                        .build())
                .userInformation(UserInformation.builder()
                        .username("admin")
                        .email("<EMAIL>")
                        .enabled(true)
                        .build())
                .build();
    }

    @Test
    @DisplayName("Should rollback Person entity successfully")
    void shouldRollbackPersonEntitySuccessfully() {
        // Given
        UserCreationContext contextWithEntity = testContext
                .withCreatedEntity(testEntity);

        Response mockResponse = mock(Response.class);
        when(mockResponse.getStatus()).thenReturn(200);
        when(entityRepository.deleteEntity(anyString(), anyString(), anyString()))
                .thenReturn(Uni.createFrom().item(mockResponse));

        // When
        UniAssertSubscriber<Void> subscriber = rollbackService
                .rollbackPersonEntity(contextWithEntity)
                .subscribe().withSubscriber(UniAssertSubscriber.create());

        // Then
        subscriber.assertCompleted();
        verify(entityRepository).deleteEntity("Bearer test-token", "test-tenant", "test-entity-123");
    }

    @Test
    @DisplayName("Should skip Person entity rollback when not created")
    void shouldSkipPersonEntityRollbackWhenNotCreated() {
        // Given - context without Person entity created
        UserCreationContext contextWithoutEntity = testContext;

        // When
        UniAssertSubscriber<Void> subscriber = rollbackService
                .rollbackPersonEntity(contextWithoutEntity)
                .subscribe().withSubscriber(UniAssertSubscriber.create());

        // Then
        subscriber.assertCompleted();
        verify(entityRepository, never()).deleteEntity(anyString(), anyString(), anyString());
    }

    @Test
    @DisplayName("Should rollback Keycloak user successfully")
    void shouldRollbackKeycloakUserSuccessfully() {
        // Given
        UserCreationContext contextWithKeycloak = testContext
                .withAuthServerId("keycloak-user-123");

        doNothing().when(securityProviderService)
                .deleteUser(anyString(), anyString());

        // When
        UniAssertSubscriber<Void> subscriber = rollbackService
                .rollbackKeycloakUser(contextWithKeycloak)
                .subscribe().withSubscriber(UniAssertSubscriber.create());

        // Then
        subscriber.assertCompleted();
        verify(securityProviderService).deleteUser("test-tenant", "keycloak-user-123");
    }

    @Test
    @DisplayName("Should skip Keycloak user rollback when not created")
    void shouldSkipKeycloakUserRollbackWhenNotCreated() {
        // Given - context without Keycloak user created
        UserCreationContext contextWithoutKeycloak = testContext;

        // When
        UniAssertSubscriber<Void> subscriber = rollbackService
                .rollbackKeycloakUser(contextWithoutKeycloak)
                .subscribe().withSubscriber(UniAssertSubscriber.create());

        // Then
        subscriber.assertCompleted();
        verify(securityProviderService, never()).deleteUser(anyString(), anyString());
    }

    @Test
    @DisplayName("Should perform context-aware rollback for Person entity only")
    void shouldPerformContextAwareRollbackForPersonEntityOnly() {
        // Given - context with only Person entity created
        UserCreationContext contextWithPersonOnly = testContext
                .withCreatedEntity(testEntity);

        Response mockResponse = mock(Response.class);
        when(mockResponse.getStatus()).thenReturn(200);
        when(entityRepository.deleteEntity(anyString(), anyString(), anyString()))
                .thenReturn(Uni.createFrom().item(mockResponse));

        // When
        UniAssertSubscriber<Void> subscriber = rollbackService
                .performContextAwareRollback(contextWithPersonOnly)
                .subscribe().withSubscriber(UniAssertSubscriber.create());

        // Then
        subscriber.assertCompleted();
        verify(entityRepository).deleteEntity("Bearer test-token", "test-tenant", "test-entity-123");
        verify(securityProviderService, never()).deleteUser(anyString(), anyString());
    }

    @Test
    @DisplayName("Should perform context-aware rollback for Person and Keycloak")
    void shouldPerformContextAwareRollbackForPersonAndKeycloak() {
        // Given - context with Person entity and Keycloak user created
        UserCreationContext contextWithBoth = testContext
                .withCreatedEntity(testEntity)
                .withAuthServerId("keycloak-user-123");

        Response mockResponse = mock(Response.class);
        when(mockResponse.getStatus()).thenReturn(200);
        when(entityRepository.deleteEntity(anyString(), anyString(), anyString()))
                .thenReturn(Uni.createFrom().item(mockResponse));
        doNothing().when(securityProviderService)
                .deleteUser(anyString(), anyString());

        // When
        UniAssertSubscriber<Void> subscriber = rollbackService
                .performContextAwareRollback(contextWithBoth)
                .subscribe().withSubscriber(UniAssertSubscriber.create());

        // Then
        subscriber.assertCompleted();
        verify(securityProviderService).deleteUser("test-tenant", "keycloak-user-123");
        verify(entityRepository).deleteEntity("Bearer test-token", "test-tenant", "test-entity-123");
    }

    @Test
    @DisplayName("Should perform full rollback when permissions assigned")
    void shouldPerformFullRollbackWhenPermissionsAssigned() {
        // Given - context with everything created including permissions
        UserCreationContext contextWithEverything = testContext
                .withCreatedEntity(testEntity)
                .withAuthServerId("keycloak-user-123")
                .withPermissionsAssigned();

        Response mockResponse = mock(Response.class);
        when(mockResponse.getStatus()).thenReturn(200);
        when(entityRepository.deleteEntity(anyString(), anyString(), anyString()))
                .thenReturn(Uni.createFrom().item(mockResponse));
        doNothing().when(securityProviderService)
                .deleteUser(anyString(), anyString());

        // When
        UniAssertSubscriber<Void> subscriber = rollbackService
                .performContextAwareRollback(contextWithEverything)
                .subscribe().withSubscriber(UniAssertSubscriber.create());

        // Then
        subscriber.assertCompleted();
        verify(securityProviderService).deleteUser("test-tenant", "keycloak-user-123");
        verify(entityRepository).deleteEntity("Bearer test-token", "test-tenant", "test-entity-123");
    }

    @Test
    @DisplayName("Should skip rollback when nothing was created")
    void shouldSkipRollbackWhenNothingWasCreated() {
        // Given - context with nothing created
        UserCreationContext emptyContext = testContext;

        // When
        UniAssertSubscriber<Void> subscriber = rollbackService
                .performContextAwareRollback(emptyContext)
                .subscribe().withSubscriber(UniAssertSubscriber.create());

        // Then
        subscriber.assertCompleted();
        verify(entityRepository, never()).deleteEntity(anyString(), anyString(), anyString());
        verify(securityProviderService, never()).deleteUser(anyString(), anyString());
    }

    @Test
    @DisplayName("Should handle rollback failures gracefully")
    void shouldHandleRollbackFailuresGracefully() {
        // Given
        UserCreationContext contextWithEntity = testContext
                .withCreatedEntity(testEntity);

        when(entityRepository.deleteEntity(anyString(), anyString(), anyString()))
                .thenReturn(Uni.createFrom().failure(new RuntimeException("Database error")));

        // When
        UniAssertSubscriber<Void> subscriber = rollbackService
                .rollbackPersonEntity(contextWithEntity)
                .subscribe().withSubscriber(UniAssertSubscriber.create());

        // Then - Should complete even if rollback fails (logged but not propagated)
        subscriber.assertCompleted();
        verify(entityRepository).deleteEntity("Bearer test-token", "test-tenant", "test-entity-123");
    }
}
