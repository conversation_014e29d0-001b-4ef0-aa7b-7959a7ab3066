# User Protection Refactoring

## Problem Statement

The original code had hardcoded admin/superadmin checks scattered throughout the UserService:

```java
// BEFORE: Hardcoded, scattered checks
if (keycloakUser.getUsername().equalsIgnoreCase("admin") ||
    keycloakUser.getUsername().equalsIgnoreCase("superadmin")) {
    throw new PermissionDeniedException(tenantId, 
        "You are not allowed to enable or disable an admin or super admin user");
}

// Similar checks in multiple methods with slight variations
if (existingKeycloakUser.getUsername().equalsIgnoreCase(RoleConstant.ADMIN) ||
    existingKeycloakUser.getUsername().equalsIgnoreCase(RoleConstant.SUPER_ADMIN)) {
    throw new PermissionDeniedException(tenantId, 
        "You are not allowed to update an admin or super admin user");
}
```

### Issues with Original Approach:

1. **Magic Strings**: Hardcoded "admin" and "superadmin" strings
2. **Code Duplication**: Similar checks repeated across methods
3. **Inconsistent Error Messages**: Different wording for similar restrictions
4. **Hard to Extend**: Adding new protected users requires code changes
5. **No Configuration**: Protection rules buried in code
6. **Difficult to Test**: Complex setup required to test protection logic

## Solution: UserProtectionService

### 1. Created Dedicated Service

```java
@ApplicationScoped
public class UserProtectionService {
    
    @ConfigProperty(name = "application.security.protected-usernames")
    Set<String> protectedUsernames;
    
    @ConfigProperty(name = "application.security.protected-roles")
    Set<String> protectedRoles;
    
    public void validateUserStatusChange(String tenantId, UserInformation user, boolean targetStatus);
    public void validateUserCanBeUpdated(String tenantId, UserInformation user);
    public void validateUserCanBeDeleted(String tenantId, UserInformation user);
    public boolean isUserProtected(UserInformation user);
    public UserProtectionInfo getUserProtectionInfo(UserInformation user);
}
```

### 2. Configuration-Driven Protection

```yaml
# application.yml
application:
  security:
    protected-usernames: ${PROTECTED_USERNAMES:admin,superadmin}
    protected-roles: ${PROTECTED_ROLES:SUPER_ADMIN,SYSTEM_ADMIN}
```

### 3. Rich Domain Model

```java
@Data
@Builder
public class UserProtectionInfo {
    boolean isProtected;
    ProtectionReason primaryReason;
    String description;
    Set<RestrictedOperation> restrictedOperations;
    String additionalContext;
    
    public enum ProtectionReason {
        USERNAME_BASED, ROLE_BASED, SYSTEM_ACCOUNT, CUSTOM_RULE, NOT_PROTECTED
    }
    
    public enum RestrictedOperation {
        STATUS_CHANGE, UPDATE, DELETE, ROLE_MODIFICATION, PASSWORD_RESET
    }
}
```

## Benefits Achieved

### 1. Single Responsibility Principle
- **UserProtectionService**: Only handles user protection logic
- **UserService**: Focuses on user operations, delegates protection to specialized service

### 2. Configuration Over Code
```java
// BEFORE: Hardcoded in multiple places
if (user.getUsername().equalsIgnoreCase("admin") || 
    user.getUsername().equalsIgnoreCase("superadmin"))

// AFTER: Configuration-driven
@ConfigProperty(name = "application.security.protected-usernames")
Set<String> protectedUsernames;
```

### 3. Extensibility
- Add new protected users via configuration
- Support role-based protection
- Easy to add new protection rules
- Environment-specific configurations

### 4. Consistent Error Handling
```java
// BEFORE: Inconsistent messages
"You are not allowed to enable or disable an admin or super admin user"
"You are not allowed to update an admin or super admin user"

// AFTER: Consistent, contextual messages
public String getErrorMessageForOperation(RestrictedOperation operation) {
    return String.format("You are not allowed to perform %s on %s", 
            operation.getDescription().toLowerCase(), 
            description.toLowerCase());
}
```

### 5. Improved Testability

```java
// BEFORE: Complex test setup
@Test
void testAdminProtection() {
    // Mock 12+ dependencies
    // Setup entire user creation workflow
    // Test entire create() method just to verify admin protection
}

// AFTER: Simple, focused tests
@Test
void shouldProtectAdminUser() {
    UserInformation admin = createUserWithUsername("admin");
    assertThrows(PermissionDeniedException.class, () -> 
        userProtectionService.validateUserStatusChange("tenant", admin, false)
    );
}
```

## Clean Code Principles Applied

### 1. Meaningful Names
- `UserProtectionService` clearly indicates purpose
- `validateUserStatusChange()` describes exact operation
- `UserProtectionInfo` provides rich context

### 2. Small, Focused Methods
```java
// Each method has single responsibility
public void validateUserStatusChange(String tenantId, UserInformation user, boolean targetStatus) {
    validateUserNotProtected(tenantId, user, "enable or disable");
    validateStatusChangeIsValid(tenantId, user, targetStatus);
}
```

### 3. No Magic Numbers/Strings
```java
// BEFORE: Magic strings
if (user.getUsername().equalsIgnoreCase("admin"))

// AFTER: Configuration-driven
protectedUsernames.stream()
    .anyMatch(protectedUsername -> protectedUsername.equalsIgnoreCase(user.getUsername()))
```

### 4. Self-Documenting Code
```java
// Method names clearly indicate what they do
public boolean isUserProtected(UserInformation user);
public UserProtectionInfo getUserProtectionInfo(UserInformation user);
public void validateUserCanBeDeleted(String tenantId, UserInformation user);
```

## Usage Examples

### 1. Simple Protection Check
```java
// BEFORE: Scattered hardcoded checks
if (user.getUsername().equalsIgnoreCase("admin") || 
    user.getUsername().equalsIgnoreCase("superadmin")) {
    throw new PermissionDeniedException(tenantId, "Cannot modify admin user");
}

// AFTER: Clean service call
userProtectionService.validateUserCanBeUpdated(tenantId, user);
```

### 2. Rich Protection Information
```java
UserProtectionInfo info = userProtectionService.getUserProtectionInfo(user);
if (info.isOperationRestricted(RestrictedOperation.STATUS_CHANGE)) {
    String errorMessage = info.getErrorMessageForOperation(RestrictedOperation.STATUS_CHANGE);
    throw new PermissionDeniedException(tenantId, errorMessage);
}
```

### 3. Configuration Flexibility
```bash
# Development environment
PROTECTED_USERNAMES=admin,superadmin,dev-admin

# Production environment  
PROTECTED_USERNAMES=admin,superadmin,prod-admin,system
PROTECTED_ROLES=SUPER_ADMIN,SYSTEM_ADMIN,PROD_ADMIN
```

## Junior Developer Benefits

### 1. Clear Intent
- Service name clearly indicates purpose
- Method names describe exact operations
- No need to understand complex business rules

### 2. Easy to Extend
```java
// Want to add new protection rule? Just add a method:
public void validateUserCanChangePassword(String tenantId, UserInformation user) {
    validateUserNotProtected(tenantId, user, "change password for");
}
```

### 3. Simple Testing
- Each protection rule can be tested independently
- No complex mock setup required
- Clear test scenarios

### 4. Configuration Understanding
- Protection rules visible in application.yml
- Environment-specific overrides clear
- No need to hunt through code for hardcoded values

## Conclusion

This refactoring transforms hardcoded, scattered protection logic into a clean, configurable, and maintainable system. The benefits include:

- **Better Separation of Concerns**: Protection logic isolated in dedicated service
- **Configuration-Driven**: Easy to modify without code changes
- **Extensible**: Support for username-based and role-based protection
- **Testable**: Simple, focused unit tests
- **Maintainable**: Clear, self-documenting code
- **Junior-Friendly**: Easy to understand and extend

The refactoring demonstrates how applying Clean Code principles and SOLID design can transform problematic code into professional, maintainable software.
