stages:
  - package
  - build
  - push
  - deploy
  - test
  - security-scan

include:
  - project: "tripudiotech/others/ci-templates"
    file:
      - package.yaml
      - build.yaml
      - push.yaml
      - deploy-v3.yaml
      - auto-test-v3.yaml
      - security-scan.yaml

variables:
  SERVICE_NAME: auth-mservice
  QA_HOST: "$CI_COMMIT_REF_SLUG-auth.qa.glideyoke.com"
  QA_URL: "http://$CI_COMMIT_REF_SLUG-auth.qa.glideyoke.com"
  QA_RELEASE: "$CI_COMMIT_REF_SLUG-auth"
  QA_NAMESPACE: tripudiotech-qa

maven-package:
  before_script: []